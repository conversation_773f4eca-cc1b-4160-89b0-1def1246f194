using UnityEngine;

public class CallGenerateMethod : MonoBehaviour
{
    void Start()
    {
        // 查找地牢场景生成器
        DungeonSceneGenerator generator = FindObjectOfType<DungeonSceneGenerator>();
        if (generator != null)
        {
            // 调用生成方法
            generator.GenerateDungeonScene();
            
            // 生成完成后销毁这个临时对象
            Destroy(this.gameObject);
        }
        else
        {
            Debug.LogError("未找到地牢场景生成器！");
        }
    }
}
