using UnityEngine;
using UnityEditor;

public class DungeonSceneGenerator : MonoBeh<PERSON><PERSON>
{
    [Header("地牢设置")]
    public float dungeonWidth = 20f;
    public float dungeonLength = 20f;
    public float wallHeight = 5f;
    public float wallThickness = 1f;

    [Header("材质颜色")]
    public Color floorColor = new Color(0.4f, 0.3f, 0.2f); // 棕色地板
    public Color wallColor = new Color(0.5f, 0.5f, 0.5f);  // 灰色墙壁
    public Color dragonColor = new Color(0.2f, 0.6f, 0.2f); // 绿色龙
    public Color treasureColor = new Color(0.8f, 0.6f, 0.1f); // 金色宝箱

    [ContextMenu("生成地牢场景")]
    public void GenerateDungeonScene()
    {
        // 清理现有场景
        ClearScene();

        // 创建地牢结构
        CreateFloor();
        CreateWalls();
        CreateCeiling();

        // 创建核心元素
        CreateDragon();
        CreateTreasureChest();

        // 创建装饰元素
        CreatePillars();
        CreateTorches();
        CreateStairs();
        CreateDecorations();

        // 设置照明
        SetupLighting();

        // 设置相机
        SetupCamera();

        Debug.Log("低多边形地牢场景生成完成！");
    }

    void ClearScene()
    {
        // 删除除了主相机和定向光之外的所有对象
        GameObject[] allObjects = FindObjectsOfType<GameObject>();
        foreach (GameObject obj in allObjects)
        {
            if (obj.name != "Main Camera" && obj.name != "Directional Light" && obj != this.gameObject)
            {
                DestroyImmediate(obj);
            }
        }
    }

    void CreateFloor()
    {
        GameObject floor = GameObject.CreatePrimitive(PrimitiveType.Cube);
        floor.name = "地板";
        floor.transform.position = Vector3.zero;
        floor.transform.localScale = new Vector3(dungeonWidth, 0.2f, dungeonLength);

        // 创建材质
        Material floorMaterial = CreateLowPolyMaterial(floorColor);
        floor.GetComponent<Renderer>().material = floorMaterial;
    }

    void CreateWalls()
    {
        // 前墙
        CreateWall("前墙", new Vector3(0, wallHeight/2, dungeonLength/2), new Vector3(dungeonWidth, wallHeight, wallThickness));
        // 后墙
        CreateWall("后墙", new Vector3(0, wallHeight/2, -dungeonLength/2), new Vector3(dungeonWidth, wallHeight, wallThickness));
        // 左墙
        CreateWall("左墙", new Vector3(-dungeonWidth/2, wallHeight/2, 0), new Vector3(wallThickness, wallHeight, dungeonLength));
        // 右墙
        CreateWall("右墙", new Vector3(dungeonWidth/2, wallHeight/2, 0), new Vector3(wallThickness, wallHeight, dungeonLength));
    }

    void CreateWall(string name, Vector3 position, Vector3 scale)
    {
        GameObject wall = GameObject.CreatePrimitive(PrimitiveType.Cube);
        wall.name = name;
        wall.transform.position = position;
        wall.transform.localScale = scale;

        Material wallMaterial = CreateLowPolyMaterial(wallColor);
        wall.GetComponent<Renderer>().material = wallMaterial;
    }

    void CreateCeiling()
    {
        GameObject ceiling = GameObject.CreatePrimitive(PrimitiveType.Cube);
        ceiling.name = "天花板";
        ceiling.transform.position = new Vector3(0, wallHeight, 0);
        ceiling.transform.localScale = new Vector3(dungeonWidth, 0.2f, dungeonLength);

        Material ceilingMaterial = CreateLowPolyMaterial(wallColor);
        ceiling.GetComponent<Renderer>().material = ceilingMaterial;
    }

    void CreateDragon()
    {
        GameObject dragon = new GameObject("龙");
        dragon.transform.position = new Vector3(0, 1f, 2f);

        // 龙身体
        GameObject body = GameObject.CreatePrimitive(PrimitiveType.Capsule);
        body.name = "龙身";
        body.transform.parent = dragon.transform;
        body.transform.localPosition = Vector3.zero;
        body.transform.localScale = new Vector3(2f, 1f, 3f);
        body.transform.rotation = Quaternion.Euler(0, 0, 90);

        // 龙头
        GameObject head = GameObject.CreatePrimitive(PrimitiveType.Sphere);
        head.name = "龙头";
        head.transform.parent = dragon.transform;
        head.transform.localPosition = new Vector3(0, 0.5f, 2f);
        head.transform.localScale = new Vector3(1.5f, 1.2f, 1.8f);

        // 龙尾
        GameObject tail = GameObject.CreatePrimitive(PrimitiveType.Capsule);
        tail.name = "龙尾";
        tail.transform.parent = dragon.transform;
        tail.transform.localPosition = new Vector3(0, 0.2f, -2.5f);
        tail.transform.localScale = new Vector3(0.8f, 0.8f, 2f);
        tail.transform.rotation = Quaternion.Euler(0, 0, 90);

        // 龙翅膀
        CreateWing(dragon.transform, "左翅膀", new Vector3(-1.5f, 0.8f, 0.5f));
        CreateWing(dragon.transform, "右翅膀", new Vector3(1.5f, 0.8f, 0.5f));

        // 应用龙的材质
        Material dragonMaterial = CreateLowPolyMaterial(dragonColor);
        ApplyMaterialToChildren(dragon, dragonMaterial);

        // 让龙面向宝箱方向
        dragon.transform.LookAt(new Vector3(0, 1f, -3f));
    }

    void CreateWing(Transform parent, string name, Vector3 position)
    {
        GameObject wing = GameObject.CreatePrimitive(PrimitiveType.Cube);
        wing.name = name;
        wing.transform.parent = parent;
        wing.transform.localPosition = position;
        wing.transform.localScale = new Vector3(0.2f, 2f, 3f);
        wing.transform.rotation = Quaternion.Euler(0, 20f, 0);
    }

    void CreateTreasureChest()
    {
        GameObject treasure = new GameObject("宝箱");
        treasure.transform.position = new Vector3(0, 0.5f, -3f);

        // 宝箱底部
        GameObject chestBase = GameObject.CreatePrimitive(PrimitiveType.Cube);
        chestBase.name = "宝箱底部";
        chestBase.transform.parent = treasure.transform;
        chestBase.transform.localPosition = Vector3.zero;
        chestBase.transform.localScale = new Vector3(1.5f, 0.8f, 1f);

        // 宝箱盖子
        GameObject chestLid = GameObject.CreatePrimitive(PrimitiveType.Cube);
        chestLid.name = "宝箱盖子";
        chestLid.transform.parent = treasure.transform;
        chestLid.transform.localPosition = new Vector3(0, 0.6f, -0.2f);
        chestLid.transform.localScale = new Vector3(1.5f, 0.4f, 0.6f);
        chestLid.transform.rotation = Quaternion.Euler(-30f, 0, 0);

        // 金币效果
        for (int i = 0; i < 5; i++)
        {
            GameObject coin = GameObject.CreatePrimitive(PrimitiveType.Cylinder);
            coin.name = "金币" + i;
            coin.transform.parent = treasure.transform;
            coin.transform.localPosition = new Vector3(
                Random.Range(-0.3f, 0.3f),
                0.5f + i * 0.1f,
                Random.Range(-0.2f, 0.2f)
            );
            coin.transform.localScale = new Vector3(0.3f, 0.05f, 0.3f);
        }

        // 应用宝箱材质
        Material treasureMaterial = CreateLowPolyMaterial(treasureColor);
        ApplyMaterialToChildren(treasure, treasureMaterial);
    }

    Material CreateLowPolyMaterial(Color color)
    {
        Material material = new Material(Shader.Find("Standard"));
        material.color = color;
        material.SetFloat("_Smoothness", 0.1f); // 低光滑度
        material.SetFloat("_Metallic", 0.0f);   // 非金属
        return material;
    }

    void ApplyMaterialToChildren(GameObject parent, Material material)
    {
        Renderer[] renderers = parent.GetComponentsInChildren<Renderer>();
        foreach (Renderer renderer in renderers)
        {
            renderer.material = material;
        }
    }

    void CreatePillars()
    {
        // 创建四个角落的石柱
        Vector3[] pillarPositions = {
            new Vector3(-7f, wallHeight/2, -7f),
            new Vector3(7f, wallHeight/2, -7f),
            new Vector3(-7f, wallHeight/2, 7f),
            new Vector3(7f, wallHeight/2, 7f)
        };

        for (int i = 0; i < pillarPositions.Length; i++)
        {
            GameObject pillar = GameObject.CreatePrimitive(PrimitiveType.Cylinder);
            pillar.name = "石柱" + (i + 1);
            pillar.transform.position = pillarPositions[i];
            pillar.transform.localScale = new Vector3(1f, wallHeight, 1f);

            Material pillarMaterial = CreateLowPolyMaterial(wallColor);
            pillar.GetComponent<Renderer>().material = pillarMaterial;
        }
    }

    void CreateTorches()
    {
        // 在墙壁上创建火把
        Vector3[] torchPositions = {
            new Vector3(-5f, 3f, dungeonLength/2 - 0.6f),
            new Vector3(5f, 3f, dungeonLength/2 - 0.6f),
            new Vector3(-dungeonWidth/2 + 0.6f, 3f, -5f),
            new Vector3(dungeonWidth/2 - 0.6f, 3f, -5f)
        };

        for (int i = 0; i < torchPositions.Length; i++)
        {
            GameObject torch = new GameObject("火把" + (i + 1));
            torch.transform.position = torchPositions[i];

            // 火把杆
            GameObject pole = GameObject.CreatePrimitive(PrimitiveType.Cylinder);
            pole.name = "火把杆";
            pole.transform.parent = torch.transform;
            pole.transform.localPosition = Vector3.zero;
            pole.transform.localScale = new Vector3(0.1f, 1f, 0.1f);

            // 火把头
            GameObject flame = GameObject.CreatePrimitive(PrimitiveType.Sphere);
            flame.name = "火焰";
            flame.transform.parent = torch.transform;
            flame.transform.localPosition = new Vector3(0, 0.8f, 0);
            flame.transform.localScale = new Vector3(0.3f, 0.3f, 0.3f);

            // 应用材质
            Material torchMaterial = CreateLowPolyMaterial(new Color(0.4f, 0.2f, 0.1f)); // 棕色
            pole.GetComponent<Renderer>().material = torchMaterial;

            Material flameMaterial = CreateLowPolyMaterial(new Color(1f, 0.5f, 0f)); // 橙色火焰
            flame.GetComponent<Renderer>().material = flameMaterial;

            // 添加点光源
            GameObject light = new GameObject("火把光源");
            light.transform.parent = torch.transform;
            light.transform.localPosition = new Vector3(0, 0.8f, 0);
            Light lightComponent = light.AddComponent<Light>();
            lightComponent.type = LightType.Point;
            lightComponent.color = new Color(1f, 0.7f, 0.3f);
            lightComponent.intensity = 2f;
            lightComponent.range = 8f;
        }
    }

    void CreateStairs()
    {
        // 在地牢一角创建楼梯
        GameObject stairs = new GameObject("楼梯");
        stairs.transform.position = new Vector3(6f, 0, 6f);

        for (int i = 0; i < 5; i++)
        {
            GameObject step = GameObject.CreatePrimitive(PrimitiveType.Cube);
            step.name = "台阶" + (i + 1);
            step.transform.parent = stairs.transform;
            step.transform.localPosition = new Vector3(0, i * 0.3f, -i * 0.5f);
            step.transform.localScale = new Vector3(2f, 0.2f, 0.5f);

            Material stepMaterial = CreateLowPolyMaterial(wallColor);
            step.GetComponent<Renderer>().material = stepMaterial;
        }
    }

    void CreateDecorations()
    {
        // 创建一些装饰骨头
        for (int i = 0; i < 3; i++)
        {
            GameObject bone = GameObject.CreatePrimitive(PrimitiveType.Capsule);
            bone.name = "骨头" + (i + 1);
            bone.transform.position = new Vector3(
                Random.Range(-8f, 8f),
                0.2f,
                Random.Range(-8f, 8f)
            );
            bone.transform.rotation = Quaternion.Euler(0, Random.Range(0, 360), 90);
            bone.transform.localScale = new Vector3(0.2f, 1f, 0.2f);

            Material boneMaterial = CreateLowPolyMaterial(new Color(0.9f, 0.9f, 0.8f)); // 骨白色
            bone.GetComponent<Renderer>().material = boneMaterial;
        }

        // 创建一些武器装饰
        GameObject sword = GameObject.CreatePrimitive(PrimitiveType.Capsule);
        sword.name = "剑";
        sword.transform.position = new Vector3(-6f, 1f, -6f);
        sword.transform.rotation = Quaternion.Euler(0, 45f, 0);
        sword.transform.localScale = new Vector3(0.1f, 2f, 0.1f);

        Material swordMaterial = CreateLowPolyMaterial(new Color(0.7f, 0.7f, 0.8f)); // 银色
        sword.GetComponent<Renderer>().material = swordMaterial;
    }

    void SetupLighting()
    {
        // 设置环境光
        RenderSettings.ambientMode = UnityEngine.Rendering.AmbientMode.Flat;
        RenderSettings.ambientLight = new Color(0.2f, 0.2f, 0.3f); // 暗蓝色环境光

        // 找到或创建定向光
        Light directionalLight = FindObjectOfType<Light>();
        if (directionalLight == null)
        {
            GameObject lightObj = new GameObject("Directional Light");
            directionalLight = lightObj.AddComponent<Light>();
            directionalLight.type = LightType.Directional;
        }

        directionalLight.color = new Color(0.8f, 0.7f, 0.6f); // 暖色调
        directionalLight.intensity = 0.8f;
        directionalLight.transform.rotation = Quaternion.Euler(45f, -30f, 0);
    }

    void SetupCamera()
    {
        Camera mainCamera = Camera.main;
        if (mainCamera != null)
        {
            // 设置相机位置以展示整个场景
            mainCamera.transform.position = new Vector3(-8f, 6f, 8f);
            mainCamera.transform.LookAt(new Vector3(0, 2f, 0));

            // 调整相机设置
            mainCamera.fieldOfView = 60f;
            mainCamera.backgroundColor = new Color(0.1f, 0.1f, 0.15f); // 深色背景
        }
    }
}
