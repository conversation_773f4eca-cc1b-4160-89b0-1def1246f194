using UnityEngine;
using UnityEditor;

public class DirectGenerate
{
    [MenuItem("工具/直接生成地牢")]
    public static void Generate()
    {
        // 查找生成器
        DungeonSceneGenerator generator = Object.FindObjectOfType<DungeonSceneGenerator>();
        if (generator == null)
        {
            // 如果没有找到，创建一个
            GameObject generatorObj = new GameObject("地牢场景生成器");
            generator = generatorObj.AddComponent<DungeonSceneGenerator>();
        }
        
        // 调用生成方法
        generator.GenerateDungeonScene();
        
        Debug.Log("地牢场景生成完成！");
    }
}
