using UnityEngine;
using UnityEditor;

[CustomEditor(typeof(DungeonSceneGenerator))]
public class DungeonSceneGeneratorEditor : Editor
{
    public override void OnInspectorGUI()
    {
        DrawDefaultInspector();

        DungeonSceneGenerator generator = (DungeonSceneGenerator)target;

        GUILayout.Space(10);

        if (GUILayout.Button("生成地牢场景", GUILayout.Height(30)))
        {
            generator.GenerateDungeonScene();
        }

        GUILayout.Space(5);

        EditorGUILayout.HelpBox("点击按钮将在当前场景中生成一个完整的低多边形地牢场景，包含龙、宝箱、石柱、火把等元素。", MessageType.Info);
    }
}

public static class DungeonSceneGeneratorMenu
{
    [MenuItem("工具/创建地牢场景生成器")]
    public static void CreateDungeonGenerator()
    {
        GameObject generatorObj = new GameObject("地牢场景生成器");
        generatorObj.AddComponent<DungeonSceneGenerator>();
        Selection.activeGameObject = generatorObj;

        EditorGUIUtility.PingObject(generatorObj);
    }
}
