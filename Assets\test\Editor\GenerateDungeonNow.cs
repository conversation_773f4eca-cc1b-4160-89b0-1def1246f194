using UnityEngine;
using UnityEditor;

public class GenerateDungeonNow : EditorWindow
{
    [MenuItem("工具/立即生成地牢场景")]
    public static void GenerateNow()
    {
        // 查找或创建生成器
        DungeonSceneGenerator generator = FindObjectOfType<DungeonSceneGenerator>();
        if (generator == null)
        {
            GameObject generatorObj = new GameObject("地牢场景生成器");
            generator = generatorObj.AddComponent<DungeonSceneGenerator>();
        }
        
        // 生成场景
        generator.GenerateDungeonScene();
        
        Debug.Log("地牢场景生成完成！");
    }
}
