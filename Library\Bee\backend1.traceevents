{ "cat":"", "pid":12345, "tid":0, "ts":0, "ph":"M", "name":"process_name", "args": { "name":"bee_backend" } }
,{ "pid":12345, "tid":0, "ts":1748104828340591, "dur":62, "ph":"X", "name": "IPC_Client_InitializeAndConnectToParent",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1748104828340711, "dur":5923, "ph":"X", "name": "DriverInitData",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1748104828346677, "dur":9569, "ph":"X", "name": "RemoveStaleOutputs",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1748104828356570, "dur":119, "ph":"X", "name": "<PERSON><PERSON>",  "args": { "detail":"PrepareNodes" }}
,{ "pid":12345, "tid":0, "ts":1748104828356690, "dur":1174, "ph":"X", "name": "BuildQueueInit",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1748104828358279, "dur":62, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.dll_2E284A21EEA637D3.mvfrm" }}
,{ "pid":12345, "tid":0, "ts":1748104828359327, "dur":152, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HotReloadModule.dll_8779DC654B49A42C.mvfrm" }}
,{ "pid":12345, "tid":0, "ts":1748104828359665, "dur":77, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PhysicsModule.dll_CA977A7B2B73956D.mvfrm" }}
,{ "pid":12345, "tid":0, "ts":1748104828359957, "dur":57, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubstanceModule.dll_97711FF90AF13DF6.mvfrm" }}
,{ "pid":12345, "tid":0, "ts":1748104828360685, "dur":90, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.WindModule.dll_D2A05FE9B1E621B6.mvfrm" }}
,{ "pid":12345, "tid":0, "ts":1748104828360905, "dur":78, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WindowsStandalone.Extensions.dll_6F29ACDB6AE0CD9E.mvfrm" }}
,{ "pid":12345, "tid":0, "ts":1748104828362772, "dur":63, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.rsp2" }}
,{ "pid":12345, "tid":0, "ts":1748104828364137, "dur":67, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.dll.mvfrm" }}
,{ "pid":12345, "tid":0, "ts":1748104828364468, "dur":50, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.dll.mvfrm.rsp" }}
,{ "pid":12345, "tid":0, "ts":1748104828365208, "dur":57, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.RenderPipelines.Core.Runtime.ref.dll_CE6A42C97D96EB0A.mvfrm" }}
,{ "pid":12345, "tid":0, "ts":1748104828366011, "dur":70, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll (+2 others)" }}
,{ "pid":12345, "tid":0, "ts":1748104828366285, "dur":55, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.SpatialTracking.UnityAdditionalFile.txt" }}
,{ "pid":12345, "tid":0, "ts":1748104828366919, "dur":75, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Formats.Fbx.Runtime.rsp2" }}
,{ "pid":12345, "tid":0, "ts":1748104828367874, "dur":54, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Services.Core.Internal.dll.mvfrm.rsp" }}
,{ "pid":12345, "tid":0, "ts":1748104828371363, "dur":128, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Services.Core.Threading.UnityAdditionalFile.txt" }}
,{ "pid":12345, "tid":0, "ts":1748104828371591, "dur":68, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Services.Core.Threading.dll.mvfrm.rsp" }}
,{ "pid":12345, "tid":0, "ts":1748104828372703, "dur":106, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Services.Core.Networking.rsp2" }}
,{ "pid":12345, "tid":0, "ts":1748104828373407, "dur":242, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.ShaderGraph.Editor.ref.dll_0F50152946DB09D0.mvfrm" }}
,{ "pid":12345, "tid":0, "ts":1748104828374416, "dur":98, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Services.Core.Editor.dll.mvfrm.rsp" }}
,{ "pid":12345, "tid":0, "ts":1748104828374810, "dur":66, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.2D.Tilemap.Editor.ref.dll_436C0E2610862891.mvfrm" }}
,{ "pid":12345, "tid":0, "ts":1748104828379575, "dur":793, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.XR.LegacyInputHelpers.UnityAdditionalFile.txt" }}
,{ "pid":12345, "tid":0, "ts":1748104828380713, "dur":67, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.Advertisements.Editor.dll.mvfrm.rsp" }}
,{ "pid":12345, "tid":0, "ts":1748104828381026, "dur":63, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityGLTF.Interactivity.Runtime.ref.dll_555A57A14426A504.mvfrm" }}
,{ "pid":12345, "tid":0, "ts":1748104828381451, "dur":67, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"WriteText Library/Bee/artifacts/1900b0aE.dag/UnityGLTF.Interactivity.VisualScriptingInstall.dll.mvfrm.rsp" }}
,{ "pid":12345, "tid":0, "ts":1748104828381785, "dur":70, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles Library/ScriptAssemblies/Unity.Rider.Editor.dll" }}
,{ "pid":12345, "tid":0, "ts":1748104828384147, "dur":1496, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"WriteText Library/Bee/artifacts/1900b0aE.dag/McpUnity.Editor.rsp" }}
,{ "pid":12345, "tid":0, "ts":1748104828385696, "dur":192, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"WriteText Library/Bee/artifacts/1900b0aE.dag/McpUnity.Editor.dll.mvfrm.rsp" }}
,{ "pid":12345, "tid":0, "ts":1748104828386136, "dur":94, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles Library/ScriptAssemblies/Unity.AI.Navigation.Editor.pdb" }}
,{ "pid":12345, "tid":0, "ts":1748104828386324, "dur":51, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.UnityAdditionalFile.txt" }}
,{ "pid":12345, "tid":0, "ts":1748104828386716, "dur":78, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles Library/ScriptAssemblies/Unity.Services.Core.Internal.pdb" }}
,{ "pid":12345, "tid":0, "ts":1748104828386981, "dur":64, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles Library/ScriptAssemblies/UnityEngine.XR.LegacyInputHelpers.dll" }}
,{ "pid":12345, "tid":0, "ts":1748104828387321, "dur":55, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles Library/ScriptAssemblies/Unity.AI.Navigation.Updater.pdb" }}
,{ "pid":12345, "tid":0, "ts":1748104828388499, "dur":60, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles Library/ScriptAssemblies/UnityGLTF.Plugins.pdb" }}
,{ "pid":12345, "tid":0, "ts":1748104828388768, "dur":52, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"WriteText Library/Bee/artifacts/1900b0aE.dag/UnityGLTF.ShaderGraph.rsp" }}
,{ "pid":12345, "tid":0, "ts":1748104828389072, "dur":66, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Services.Core.Environments.Editor.rsp2" }}
,{ "pid":12345, "tid":0, "ts":1748104828357926, "dur":32131, "ph":"X", "name": "EnqueueRequestedNodes",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1748104828390075, "dur":141785, "ph":"X", "name": "WaitForBuildFinished", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1748104828531862, "dur":408, "ph":"X", "name": "JoinBuildThread",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1748104828532322, "dur":462291, "ph":"X", "name": "JoinBuildThread",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1748104828994729, "dur":54, "ph":"X", "name": "BuildQueueDestroyTail",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1748104828994809, "dur":2790, "ph":"X", "name": "Tundra",  "args": { "detail":"Write AllBuiltNodes" }}
,{ "pid":12345, "tid":1, "ts":1748104828358211, "dur":31879, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1748104828390150, "dur":124, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.SceneTemplateModule.dll" }}
,{ "pid":12345, "tid":1, "ts":1748104828390276, "dur":444, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe" }}
,{ "pid":12345, "tid":1, "ts":1748104828390117, "dur":605, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneTemplateModule.dll_CB6ADEE492ABE367.mvfrm" }}
,{ "pid":12345, "tid":1, "ts":1748104828390723, "dur":136, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1748104828390886, "dur":53, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.TextRenderingModule.dll" }}
,{ "pid":12345, "tid":1, "ts":1748104828390884, "dur":58, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextRenderingModule.dll_A14EF3475E0117E5.mvfrm" }}
,{ "pid":12345, "tid":1, "ts":1748104828390943, "dur":93, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1748104828391049, "dur":57, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll" }}
,{ "pid":12345, "tid":1, "ts":1748104828391047, "dur":78, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll_D2689F8288ABEF08.mvfrm" }}
,{ "pid":12345, "tid":1, "ts":1748104828391126, "dur":55, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1748104828391182, "dur":77, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll_D2689F8288ABEF08.mvfrm" }}
,{ "pid":12345, "tid":1, "ts":1748104828391266, "dur":83, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.UIElementsModule.dll" }}
,{ "pid":12345, "tid":1, "ts":1748104828391263, "dur":89, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsModule.dll_1162FBC4E2DBC81E.mvfrm" }}
,{ "pid":12345, "tid":1, "ts":1748104828391353, "dur":53, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1748104828391418, "dur":402, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ClusterInputModule.dll" }}
,{ "pid":12345, "tid":1, "ts":1748104828391822, "dur":111, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe" }}
,{ "pid":12345, "tid":1, "ts":1748104828391415, "dur":519, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterInputModule.dll_386060D3F740CA9E.mvfrm" }}
,{ "pid":12345, "tid":1, "ts":1748104828391935, "dur":75, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1748104828392010, "dur":88, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterInputModule.dll_386060D3F740CA9E.mvfrm" }}
,{ "pid":12345, "tid":1, "ts":1748104828392114, "dur":161, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.UnityAdditionalFile.txt" }}
,{ "pid":12345, "tid":1, "ts":1748104828392276, "dur":52, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1748104828392340, "dur":236, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Services.Core.Internal.UnityAdditionalFile.txt" }}
,{ "pid":12345, "tid":1, "ts":1748104828392576, "dur":74, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1748104828392658, "dur":52, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1748104828392715, "dur":243, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteText Library/Bee/artifacts/1900b0aE.dag/UnityGLTF.Helpers.dll.mvfrm.rsp" }}
,{ "pid":12345, "tid":1, "ts":1748104828392959, "dur":59, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1748104828393029, "dur":195, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Services.Core.Configuration.rsp" }}
,{ "pid":12345, "tid":1, "ts":1748104828393286, "dur":154, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll.mvfrm.rsp" }}
,{ "pid":12345, "tid":1, "ts":1748104828393442, "dur":58, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1748104828393511, "dur":95, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1748104828393612, "dur":51, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Services.Core.Registration.rsp" }}
,{ "pid":12345, "tid":1, "ts":1748104828393664, "dur":66, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1748104828393740, "dur":136, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1748104828393891, "dur":62, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Formats.Fbx.Runtime.dll.mvfrm.rsp" }}
,{ "pid":12345, "tid":1, "ts":1748104828393954, "dur":61, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1748104828394020, "dur":264, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.rsp" }}
,{ "pid":12345, "tid":1, "ts":1748104828394383, "dur":241, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.rsp2" }}
,{ "pid":12345, "tid":1, "ts":1748104828394627, "dur":58, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1748104828394850, "dur":142, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.dll.mvfrm.rsp" }}
,{ "pid":12345, "tid":1, "ts":1748104828395033, "dur":293, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.dll.mvfrm.rsp" }}
,{ "pid":12345, "tid":1, "ts":1748104828395329, "dur":227, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.rsp" }}
,{ "pid":12345, "tid":1, "ts":1748104828395557, "dur":79, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1748104828395644, "dur":55, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1748104828395706, "dur":91, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteText Library/Bee/artifacts/1900b0aE.dag/Analytics.dll.mvfrm.rsp" }}
,{ "pid":12345, "tid":1, "ts":1748104828395798, "dur":170, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1748104828395976, "dur":54, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1748104828396035, "dur":611, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.Advertisements.DevX.Editor.rsp" }}
,{ "pid":12345, "tid":1, "ts":1748104828396647, "dur":280, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1748104828398742, "dur":619, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.ads@4.4.2\\Runtime\\Advertisement\\Dispatchers\\UnityAdsInitializationListenerMainDispatch.cs" }}
,{ "pid":12345, "tid":1, "ts":1748104828399362, "dur":1209, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.ads@4.4.2\\Runtime\\Advertisement\\Configuration.cs" }}
,{ "pid":12345, "tid":1, "ts":1748104828396967, "dur":4120, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1748104828402784, "dur":677, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.2d.sprite@1.0.0\\Editor\\SpriteEditorModule\\SpriteFrameModule\\SpriteFrameModuleView.cs" }}
,{ "pid":12345, "tid":1, "ts":1748104828401088, "dur":3361, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1748104828404449, "dur":849, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1748104828405298, "dur":1016, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1748104828406314, "dur":835, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1748104828408384, "dur":774, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\org.khronos.unitygltf@1d3d93121c\\Runtime\\Scripts\\UniformMaps\\UnlitGraphMap.cs" }}
,{ "pid":12345, "tid":1, "ts":1748104828407150, "dur":2120, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1748104828409271, "dur":845, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1748104828410116, "dur":1222, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1748104828411339, "dur":1016, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1748104828412355, "dur":1193, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1748104828413549, "dur":2677, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1748104828416227, "dur":2383, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1748104828418611, "dur":1649, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1748104828420261, "dur":1267, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1748104828421529, "dur":840, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1748104828422370, "dur":119, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1748104828422491, "dur":173, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Formats.Fbx.Runtime.dll.mvfrm" }}
,{ "pid":12345, "tid":1, "ts":1748104828422665, "dur":74, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1748104828422795, "dur":85, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.UIElementsModule.dll" }}
,{ "pid":12345, "tid":1, "ts":1748104828422993, "dur":300, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.SharedInternalsModule.dll" }}
,{ "pid":12345, "tid":1, "ts":1748104828422761, "dur":1002, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Formats.Fbx.Runtime.dll (+2 others)" }}
,{ "pid":12345, "tid":1, "ts":1748104828423764, "dur":352, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1748104828424129, "dur":89, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1748104828424227, "dur":361, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll.mvfrm" }}
,{ "pid":12345, "tid":1, "ts":1748104828424589, "dur":690, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1748104828425394, "dur":91, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.InputModule.dll" }}
,{ "pid":12345, "tid":1, "ts":1748104828425507, "dur":104, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ScreenCaptureModule.dll" }}
,{ "pid":12345, "tid":1, "ts":1748104828425924, "dur":358, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Resources.ResourceManager.dll" }}
,{ "pid":12345, "tid":1, "ts":1748104828425288, "dur":1147, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+2 others)" }}
,{ "pid":12345, "tid":1, "ts":1748104828426436, "dur":1540, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1748104828427977, "dur":126, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+2 others)" }}
,{ "pid":12345, "tid":1, "ts":1748104828428122, "dur":84, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1748104828428245, "dur":220, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.QuickSearchModule.dll" }}
,{ "pid":12345, "tid":1, "ts":1748104828429217, "dur":172, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.TerrainModule.dll" }}
,{ "pid":12345, "tid":1, "ts":1748104828429601, "dur":249, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Transactions.dll" }}
,{ "pid":12345, "tid":1, "ts":1748104828428214, "dur":2239, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.Advertisements.dll (+2 others)" }}
,{ "pid":12345, "tid":1, "ts":1748104828430454, "dur":194, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1748104828430663, "dur":66, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1748104828430739, "dur":558, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1748104828431307, "dur":113, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1748104828431432, "dur":93, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1748104828431533, "dur":73, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1748104828431615, "dur":151, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1748104828431778, "dur":435, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1748104828432225, "dur":496, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1748104828432734, "dur":893, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1748104828433628, "dur":162, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1748104828433791, "dur":153, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.Advertisements.DevX.Editor.dll.mvfrm" }}
,{ "pid":12345, "tid":1, "ts":1748104828433948, "dur":71, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1748104828434031, "dur":448, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.Advertisements.DevX.Editor.dll (+2 others)" }}
,{ "pid":12345, "tid":1, "ts":1748104828434480, "dur":136, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1748104828434633, "dur":111, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1748104828434756, "dur":57, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1748104828434813, "dur":93591, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1748104828528406, "dur":3401, "ph":"X", "name": "CheckDagSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1748104828358306, "dur":31819, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1748104828390160, "dur":118, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityWebRequestTextureModule.dll" }}
,{ "pid":12345, "tid":2, "ts":1748104828390279, "dur":422, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe" }}
,{ "pid":12345, "tid":2, "ts":1748104828390131, "dur":572, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestTextureModule.dll_7FE938DD0ACB41F9.mvfrm" }}
,{ "pid":12345, "tid":2, "ts":1748104828390725, "dur":171, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1748104828390910, "dur":60, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.TerrainModule.dll" }}
,{ "pid":12345, "tid":2, "ts":1748104828390908, "dur":66, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainModule.dll_E4DDF5EA0131DE5B.mvfrm" }}
,{ "pid":12345, "tid":2, "ts":1748104828390975, "dur":208, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1748104828391202, "dur":181, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.SceneViewModule.dll" }}
,{ "pid":12345, "tid":2, "ts":1748104828391200, "dur":346, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneViewModule.dll_765B853E8ADC8112.mvfrm" }}
,{ "pid":12345, "tid":2, "ts":1748104828391547, "dur":461, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1748104828392019, "dur":51, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\PackageCache\\com.unity.analytics@3.8.1\\Unity.Analytics.Editor.dll" }}
,{ "pid":12345, "tid":2, "ts":1748104828392017, "dur":57, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Analytics.Editor.dll_448B18A867B17122.mvfrm" }}
,{ "pid":12345, "tid":2, "ts":1748104828392075, "dur":50, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1748104828392142, "dur":168, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll.mvfrm.rsp" }}
,{ "pid":12345, "tid":2, "ts":1748104828392310, "dur":53, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1748104828392369, "dur":485, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Services.Core.Internal.rsp" }}
,{ "pid":12345, "tid":2, "ts":1748104828392855, "dur":62, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1748104828393036, "dur":50, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1748104828393093, "dur":54, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1748104828393147, "dur":99, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Services.Core.Environments.Internal.rsp2" }}
,{ "pid":12345, "tid":2, "ts":1748104828393262, "dur":61, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1748104828393342, "dur":423, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.SpatialTracking.rsp" }}
,{ "pid":12345, "tid":2, "ts":1748104828393766, "dur":58, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1748104828393908, "dur":294, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Services.Core.Editor.dll.mvfrm.rsp" }}
,{ "pid":12345, "tid":2, "ts":1748104828394269, "dur":60, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1748104828394382, "dur":211, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.UnityAdditionalFile.txt" }}
,{ "pid":12345, "tid":2, "ts":1748104828394597, "dur":51, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1748104828394741, "dur":362, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/1900b0aE.dag/UnityGLTF.Interactivity.VisualScriptingInstall.UnityAdditionalFile.txt" }}
,{ "pid":12345, "tid":2, "ts":1748104828395111, "dur":55, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1748104828395340, "dur":221, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.Advertisements.rsp" }}
,{ "pid":12345, "tid":2, "ts":1748104828395619, "dur":50, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1748104828395669, "dur":80, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/1900b0aE.dag/Analytics.rsp2" }}
,{ "pid":12345, "tid":2, "ts":1748104828395840, "dur":53, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1748104828395899, "dur":169, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.Advertisements.DevX.Editor.dll.mvfrm.rsp" }}
,{ "pid":12345, "tid":2, "ts":1748104828396069, "dur":50, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1748104828396129, "dur":1446, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1748104828397575, "dur":1070, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1748104828398646, "dur":955, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1748104828399601, "dur":858, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1748104828400460, "dur":2873, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1748104828403334, "dur":890, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1748104828404518, "dur":994, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.shadergraph@14.0.11\\Editor\\Generation\\Contexts\\TargetSetupContext.cs" }}
,{ "pid":12345, "tid":2, "ts":1748104828404225, "dur":2081, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1748104828406307, "dur":1161, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1748104828408100, "dur":1491, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\org.khronos.unitygltf@1d3d93121c\\Runtime\\Scripts\\Plugins\\Core\\ExperimentalPluginAttribute.cs" }}
,{ "pid":12345, "tid":2, "ts":1748104828407469, "dur":2310, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1748104828409780, "dur":1030, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1748104828410811, "dur":1043, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1748104828412475, "dur":556, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.textmeshpro@3.0.7\\Scripts\\Runtime\\TMP_SpriteAsset.cs" }}
,{ "pid":12345, "tid":2, "ts":1748104828411854, "dur":1555, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1748104828413410, "dur":1314, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.collab-proxy@2.5.1\\Editor\\PlasticSCM\\Views\\IncomingChanges\\Developer\\ChangeCategoryTreeViewItem.cs" }}
,{ "pid":12345, "tid":2, "ts":1748104828416079, "dur":571, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.collab-proxy@2.5.1\\Editor\\PlasticSCM\\Views\\DownloadPlasticExeDialog.cs" }}
,{ "pid":12345, "tid":2, "ts":1748104828417035, "dur":695, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.collab-proxy@2.5.1\\Editor\\PlasticSCM\\Views\\Diff\\MergeCategoryTreeViewItem.cs" }}
,{ "pid":12345, "tid":2, "ts":1748104828418885, "dur":849, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.collab-proxy@2.5.1\\Editor\\PlasticSCM\\Views\\Diff\\ClientDiffTreeViewItem.cs" }}
,{ "pid":12345, "tid":2, "ts":1748104828413410, "dur":6625, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1748104828420035, "dur":1486, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1748104828421521, "dur":861, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1748104828422382, "dur":364, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1748104828422747, "dur":184, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Autodesk.Fbx.dll.mvfrm" }}
,{ "pid":12345, "tid":2, "ts":1748104828422932, "dur":83, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1748104828423967, "dur":51, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\PackageCache\\com.autodesk.fbx@4.2.1\\Runtime\\Scripts\\FbxSkeleton.cs" }}
,{ "pid":12345, "tid":2, "ts":1748104828423021, "dur":1037, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1900b0aE.dag/Autodesk.Fbx.dll (+2 others)" }}
,{ "pid":12345, "tid":2, "ts":1748104828424064, "dur":515, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1748104828424588, "dur":214, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll.mvfrm" }}
,{ "pid":12345, "tid":2, "ts":1748104828424803, "dur":710, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1748104828425520, "dur":190, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll.mvfrm" }}
,{ "pid":12345, "tid":2, "ts":1748104828425711, "dur":57, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1748104828425805, "dur":127, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.UIBuilderModule.dll" }}
,{ "pid":12345, "tid":2, "ts":1748104828426274, "dur":307, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Runtime.CompilerServices.VisualC.dll" }}
,{ "pid":12345, "tid":2, "ts":1748104828427313, "dur":81, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\PackageCache\\com.unity.timeline@1.7.6\\Editor\\treeview\\IRowGUI.cs" }}
,{ "pid":12345, "tid":2, "ts":1748104828425775, "dur":1932, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll (+2 others)" }}
,{ "pid":12345, "tid":2, "ts":1748104828427737, "dur":279, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1748104828428017, "dur":260, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll (+2 others)" }}
,{ "pid":12345, "tid":2, "ts":1748104828428289, "dur":68, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1748104828428365, "dur":520, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Formats.Fbx.Editor.dll.mvfrm" }}
,{ "pid":12345, "tid":2, "ts":1748104828428885, "dur":93, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1748104828428984, "dur":863, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Formats.Fbx.Editor.dll (+2 others)" }}
,{ "pid":12345, "tid":2, "ts":1748104828429848, "dur":319, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1748104828430230, "dur":83, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1748104828430321, "dur":74, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1748104828430403, "dur":99, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1748104828430511, "dur":150, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1748104828430671, "dur":90, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1748104828430761, "dur":82, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles Library/ScriptAssemblies/UnityEngine.Advertisements.dll" }}
,{ "pid":12345, "tid":2, "ts":1748104828430849, "dur":147, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1748104828431006, "dur":102, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1748104828431155, "dur":355, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1748104828431521, "dur":135, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1748104828431694, "dur":122, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1748104828431825, "dur":91, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1748104828431925, "dur":307, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1748104828432244, "dur":415, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1748104828432666, "dur":1024, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1748104828433691, "dur":202, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1748104828433894, "dur":949, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1748104828434843, "dur":97022, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1748104828358280, "dur":31833, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1748104828390150, "dur":291, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityWebRequestWWWModule.dll" }}
,{ "pid":12345, "tid":3, "ts":1748104828390443, "dur":578, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe" }}
,{ "pid":12345, "tid":3, "ts":1748104828390118, "dur":904, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestWWWModule.dll_BD7D0B0137556FB5.mvfrm" }}
,{ "pid":12345, "tid":3, "ts":1748104828391022, "dur":117, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1748104828391151, "dur":210, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.GraphViewModule.dll" }}
,{ "pid":12345, "tid":3, "ts":1748104828391148, "dur":217, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphViewModule.dll_37ABA23695B9B404.mvfrm" }}
,{ "pid":12345, "tid":3, "ts":1748104828391366, "dur":217, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1748104828391592, "dur":77, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.HotReloadModule.dll" }}
,{ "pid":12345, "tid":3, "ts":1748104828391590, "dur":82, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HotReloadModule.dll_8779DC654B49A42C.mvfrm" }}
,{ "pid":12345, "tid":3, "ts":1748104828391673, "dur":84, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1748104828391771, "dur":170, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\PlaybackEngines\\AndroidPlayer\\Unity.Android.Gradle.dll" }}
,{ "pid":12345, "tid":3, "ts":1748104828391769, "dur":176, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Android.Gradle.dll_4E2129964EA4930A.mvfrm" }}
,{ "pid":12345, "tid":3, "ts":1748104828391946, "dur":62, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1748104828392028, "dur":55, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\PackageCache\\com.unity.analytics@3.8.1\\Unity.Analytics.Tracker.dll" }}
,{ "pid":12345, "tid":3, "ts":1748104828392026, "dur":61, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Analytics.Tracker.dll_B294C7C751B9DF6F.mvfrm" }}
,{ "pid":12345, "tid":3, "ts":1748104828392198, "dur":80, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1748104828392286, "dur":194, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Services.Core.dll.mvfrm.rsp" }}
,{ "pid":12345, "tid":3, "ts":1748104828392481, "dur":66, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1748104828392555, "dur":176, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.rsp2" }}
,{ "pid":12345, "tid":3, "ts":1748104828392733, "dur":60, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1748104828392804, "dur":220, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Services.Core.Configuration.UnityAdditionalFile.txt" }}
,{ "pid":12345, "tid":3, "ts":1748104828393077, "dur":136, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Services.Core.Scheduler.dll.mvfrm.rsp" }}
,{ "pid":12345, "tid":3, "ts":1748104828393214, "dur":60, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1748104828393298, "dur":73, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1748104828393379, "dur":70, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1748104828393457, "dur":177, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Services.Core.Networking.rsp2" }}
,{ "pid":12345, "tid":3, "ts":1748104828393636, "dur":57, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1748104828393722, "dur":55, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1748104828393981, "dur":334, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.dll.mvfrm.rsp" }}
,{ "pid":12345, "tid":3, "ts":1748104828394316, "dur":85, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1748104828394402, "dur":224, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.dll.mvfrm.rsp" }}
,{ "pid":12345, "tid":3, "ts":1748104828394628, "dur":50, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1748104828394845, "dur":85, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll.mvfrm.rsp" }}
,{ "pid":12345, "tid":3, "ts":1748104828394931, "dur":72, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1748104828395003, "dur":305, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll.mvfrm.rsp" }}
,{ "pid":12345, "tid":3, "ts":1748104828395309, "dur":150, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Formats.Fbx.Editor.rsp" }}
,{ "pid":12345, "tid":3, "ts":1748104828395461, "dur":55, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1748104828395522, "dur":103, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.XR.LegacyInputHelpers.rsp" }}
,{ "pid":12345, "tid":3, "ts":1748104828395672, "dur":104, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.XR.LegacyInputHelpers.rsp" }}
,{ "pid":12345, "tid":3, "ts":1748104828395836, "dur":142, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1748104828395995, "dur":54, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1748104828396082, "dur":52, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1748104828396141, "dur":1519, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1748104828397660, "dur":924, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1748104828398584, "dur":877, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1748104828399462, "dur":877, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1748104828400340, "dur":3181, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1748104828403521, "dur":1239, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1748104828404760, "dur":1078, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1748104828405838, "dur":838, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1748104828406676, "dur":996, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1748104828408364, "dur":771, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\org.khronos.unitygltf@1d3d93121c\\Runtime\\Plugins\\GLTFSerialization\\Schema\\GLTFMaterial.cs" }}
,{ "pid":12345, "tid":3, "ts":1748104828407673, "dur":1664, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1748104828409338, "dur":1535, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1748104828411023, "dur":1211, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.timeline@1.7.6\\Editor\\Recording\\TimelineRecording_Monobehaviour.cs" }}
,{ "pid":12345, "tid":3, "ts":1748104828410874, "dur":2941, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1748104828413816, "dur":2397, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1748104828418384, "dur":1252, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.mathematics@1.2.6\\Unity.Mathematics\\int2x4.gen.cs" }}
,{ "pid":12345, "tid":3, "ts":1748104828416214, "dur":3846, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1748104828420061, "dur":1473, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1748104828421534, "dur":845, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1748104828422379, "dur":217, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1748104828422598, "dur":485, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.EditorCoroutines.Editor.dll.mvfrm" }}
,{ "pid":12345, "tid":3, "ts":1748104828423084, "dur":234, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1748104828423966, "dur":51, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\PackageCache\\com.unity.editorcoroutines@1.0.0\\Editor\\EditorCoroutineUtility.cs" }}
,{ "pid":12345, "tid":3, "ts":1748104828423324, "dur":707, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.EditorCoroutines.Editor.dll (+2 others)" }}
,{ "pid":12345, "tid":3, "ts":1748104828424038, "dur":175, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1748104828424214, "dur":145, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.EditorCoroutines.Editor.dll (+2 others)" }}
,{ "pid":12345, "tid":3, "ts":1748104828424362, "dur":407, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.SpatialTracking.dll.mvfrm" }}
,{ "pid":12345, "tid":3, "ts":1748104828424770, "dur":63, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1748104828424940, "dur":50, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ImageConversionModule.dll" }}
,{ "pid":12345, "tid":3, "ts":1748104828424846, "dur":792, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.SpatialTracking.dll (+2 others)" }}
,{ "pid":12345, "tid":3, "ts":1748104828425639, "dur":343, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1748104828425991, "dur":171, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Services.Core.Telemetry.dll.mvfrm" }}
,{ "pid":12345, "tid":3, "ts":1748104828426163, "dur":55, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1748104828426274, "dur":309, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.AssetBundleModule.dll" }}
,{ "pid":12345, "tid":3, "ts":1748104828426649, "dur":50, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ScreenCaptureModule.dll" }}
,{ "pid":12345, "tid":3, "ts":1748104828426974, "dur":62, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Globalization.Calendars.dll" }}
,{ "pid":12345, "tid":3, "ts":1748104828427230, "dur":91, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Security.Principal.dll" }}
,{ "pid":12345, "tid":3, "ts":1748104828427364, "dur":157, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Xml.XmlDocument.dll" }}
,{ "pid":12345, "tid":3, "ts":1748104828426223, "dur":1392, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Services.Core.Telemetry.dll (+2 others)" }}
,{ "pid":12345, "tid":3, "ts":1748104828427616, "dur":165, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1748104828427800, "dur":207, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1748104828428007, "dur":162, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Services.Core.Telemetry.ref.dll_FA20132DD923A4D2.mvfrm" }}
,{ "pid":12345, "tid":3, "ts":1748104828428455, "dur":160, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.Contracts.dll" }}
,{ "pid":12345, "tid":3, "ts":1748104828428171, "dur":844, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll (+2 others)" }}
,{ "pid":12345, "tid":3, "ts":1748104828429016, "dur":258, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1748104828429292, "dur":330, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1748104828429633, "dur":172, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1748104828429816, "dur":160, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1748104828429992, "dur":363, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1748104828430366, "dur":253, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1748104828430629, "dur":94, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1748104828430732, "dur":94, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1748104828430840, "dur":263, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1748104828431129, "dur":79, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1748104828431216, "dur":240, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1748104828431465, "dur":229, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1748104828431703, "dur":81, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1748104828431792, "dur":89, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1748104828431898, "dur":128, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1748104828432047, "dur":91, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1748104828432146, "dur":310, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1748104828432511, "dur":1292, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1748104828433803, "dur":1021, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1748104828434824, "dur":97027, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1748104828358374, "dur":31763, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1748104828390164, "dur":117, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityWebRequestModule.dll" }}
,{ "pid":12345, "tid":4, "ts":1748104828390283, "dur":417, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe" }}
,{ "pid":12345, "tid":4, "ts":1748104828390143, "dur":559, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestModule.dll_CB6B9DA4086B1D36.mvfrm" }}
,{ "pid":12345, "tid":4, "ts":1748104828390724, "dur":144, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1748104828390891, "dur":689, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.TextCoreTextEngineModule.dll" }}
,{ "pid":12345, "tid":4, "ts":1748104828390888, "dur":710, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreTextEngineModule.dll_F2BAC5A54795B349.mvfrm" }}
,{ "pid":12345, "tid":4, "ts":1748104828391600, "dur":345, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1748104828391958, "dur":239, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.rsp2" }}
,{ "pid":12345, "tid":4, "ts":1748104828392198, "dur":63, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1748104828392362, "dur":246, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Services.Core.Internal.rsp2" }}
,{ "pid":12345, "tid":4, "ts":1748104828392609, "dur":107, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1748104828392722, "dur":359, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteText Library/Bee/artifacts/1900b0aE.dag/GLTFSerialization.dll.mvfrm.rsp" }}
,{ "pid":12345, "tid":4, "ts":1748104828393082, "dur":64, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1748104828393146, "dur":160, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/1900b0aE.dag/GLTFSerialization.dll.mvfrm.rsp" }}
,{ "pid":12345, "tid":4, "ts":1748104828393308, "dur":273, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Services.Core.Threading.rsp" }}
,{ "pid":12345, "tid":4, "ts":1748104828393582, "dur":67, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1748104828393658, "dur":164, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.rsp2" }}
,{ "pid":12345, "tid":4, "ts":1748104828393920, "dur":59, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1748104828393986, "dur":775, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.rsp" }}
,{ "pid":12345, "tid":4, "ts":1748104828394763, "dur":50, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1748104828394873, "dur":325, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.rsp" }}
,{ "pid":12345, "tid":4, "ts":1748104828395200, "dur":50, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1748104828395258, "dur":174, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.rsp" }}
,{ "pid":12345, "tid":4, "ts":1748104828395433, "dur":53, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1748104828395553, "dur":51, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1748104828395656, "dur":170, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/1900b0aE.dag/Analytics.UnityAdditionalFile.txt" }}
,{ "pid":12345, "tid":4, "ts":1748104828396001, "dur":105, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Services.Core.Environments.Editor.UnityAdditionalFile.txt" }}
,{ "pid":12345, "tid":4, "ts":1748104828396107, "dur":1388, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1748104828398812, "dur":1498, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.ide.visualstudio@2.0.22\\Editor\\ProcessRunner.cs" }}
,{ "pid":12345, "tid":4, "ts":1748104828397496, "dur":3994, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1748104828401632, "dur":680, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.2d.sprite@1.0.0\\Editor\\SpriteEditor\\SpriteEditorWindowSettings.cs" }}
,{ "pid":12345, "tid":4, "ts":1748104828402557, "dur":980, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.2d.sprite@1.0.0\\Editor\\SpriteEditor\\SpriteEditorUtility.cs" }}
,{ "pid":12345, "tid":4, "ts":1748104828403537, "dur":1569, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.2d.sprite@1.0.0\\Editor\\SpriteEditor\\SpriteEditorMenu.cs" }}
,{ "pid":12345, "tid":4, "ts":1748104828405107, "dur":776, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.2d.sprite@1.0.0\\Editor\\SpriteEditor\\SpriteEditorHandles.cs" }}
,{ "pid":12345, "tid":4, "ts":1748104828405884, "dur":1371, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.2d.sprite@1.0.0\\Editor\\ShapeEditor\\ShapeEditorRectSelection.cs" }}
,{ "pid":12345, "tid":4, "ts":1748104828407680, "dur":1483, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.2d.sprite@1.0.0\\Editor\\Interface\\ITexture.cs" }}
,{ "pid":12345, "tid":4, "ts":1748104828401491, "dur":8534, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1748104828410026, "dur":931, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1748104828410958, "dur":872, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1748104828411831, "dur":903, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1748104828413215, "dur":625, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.render-pipelines.core@14.0.11\\Runtime\\Debugging\\Prefabs\\Scripts\\DebugUIHandlerObjectPopupField.cs" }}
,{ "pid":12345, "tid":4, "ts":1748104828412735, "dur":1895, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1748104828414817, "dur":543, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.collab-proxy@2.5.1\\Editor\\PlasticSCM\\Gluon\\UpdateReport\\UpdateReportListHeaderState.cs" }}
,{ "pid":12345, "tid":4, "ts":1748104828416848, "dur":1106, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.collab-proxy@2.5.1\\Editor\\PlasticSCM\\Developer\\UpdateReport\\UpdateReportDialog.cs" }}
,{ "pid":12345, "tid":4, "ts":1748104828414631, "dur":4042, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1748104828418673, "dur":2004, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1748104828420677, "dur":834, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1748104828421512, "dur":834, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1748104828422347, "dur":108, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1748104828422460, "dur":177, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityGLTF.Helpers.dll.mvfrm" }}
,{ "pid":12345, "tid":4, "ts":1748104828422638, "dur":73, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1748104828422983, "dur":52, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityWebRequestModule.dll" }}
,{ "pid":12345, "tid":4, "ts":1748104828423283, "dur":131, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.ObjectModel.dll" }}
,{ "pid":12345, "tid":4, "ts":1748104828422720, "dur":871, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1900b0aE.dag/UnityGLTF.Helpers.dll (+2 others)" }}
,{ "pid":12345, "tid":4, "ts":1748104828423592, "dur":159, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1748104828423816, "dur":218, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll.mvfrm" }}
,{ "pid":12345, "tid":4, "ts":1748104828424035, "dur":58, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1748104828424108, "dur":267, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Services.Core.Analytics.dll.mvfrm" }}
,{ "pid":12345, "tid":4, "ts":1748104828424379, "dur":61, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1748104828424447, "dur":311, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll.mvfrm" }}
,{ "pid":12345, "tid":4, "ts":1748104828424759, "dur":95, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1748104828425019, "dur":499, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.TextRenderingModule.dll" }}
,{ "pid":12345, "tid":4, "ts":1748104828425566, "dur":255, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityWebRequestTextureModule.dll" }}
,{ "pid":12345, "tid":4, "ts":1748104828425924, "dur":70, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Diagnostics.TraceSource.dll" }}
,{ "pid":12345, "tid":4, "ts":1748104828426105, "dur":50, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Runtime.CompilerServices.VisualC.dll" }}
,{ "pid":12345, "tid":4, "ts":1748104828426273, "dur":323, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Security.SecureString.dll" }}
,{ "pid":12345, "tid":4, "ts":1748104828424862, "dur":2021, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll (+2 others)" }}
,{ "pid":12345, "tid":4, "ts":1748104828426883, "dur":251, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1748104828427168, "dur":97, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1748104828427296, "dur":645, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Updater.dll.mvfrm" }}
,{ "pid":12345, "tid":4, "ts":1748104828427942, "dur":53, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1748104828427996, "dur":260, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Updater.dll.mvfrm" }}
,{ "pid":12345, "tid":4, "ts":1748104828428454, "dur":191, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.WindModule.dll" }}
,{ "pid":12345, "tid":4, "ts":1748104828428756, "dur":218, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.IO.FileSystem.Primitives.dll" }}
,{ "pid":12345, "tid":4, "ts":1748104828428260, "dur":1046, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Updater.dll (+2 others)" }}
,{ "pid":12345, "tid":4, "ts":1748104828429307, "dur":227, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1748104828429600, "dur":713, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1748104828430329, "dur":348, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1748104828430688, "dur":96, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1748104828430785, "dur":138, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles Library/ScriptAssemblies/Unity.Timeline.dll" }}
,{ "pid":12345, "tid":4, "ts":1748104828430929, "dur":95, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1748104828431033, "dur":103, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1748104828431151, "dur":241, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1748104828431404, "dur":91, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1748104828431506, "dur":678, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1748104828432197, "dur":101, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1748104828432305, "dur":77, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles Library/ScriptAssemblies/Unity.AI.Navigation.Editor.ConversionSystem.pdb" }}
,{ "pid":12345, "tid":4, "ts":1748104828432383, "dur":123, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1748104828432515, "dur":1090, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1748104828433611, "dur":132, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1748104828433868, "dur":949, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1748104828434818, "dur":97048, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1748104828358424, "dur":31724, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1748104828390168, "dur":292, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityWebRequestAudioModule.dll" }}
,{ "pid":12345, "tid":5, "ts":1748104828390463, "dur":569, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe" }}
,{ "pid":12345, "tid":5, "ts":1748104828390153, "dur":880, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAudioModule.dll_2AF95E58815E8FA4.mvfrm" }}
,{ "pid":12345, "tid":5, "ts":1748104828391034, "dur":108, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1748104828391156, "dur":67, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.PresetsUIModule.dll" }}
,{ "pid":12345, "tid":5, "ts":1748104828391153, "dur":73, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PresetsUIModule.dll_F311DB1898120C09.mvfrm" }}
,{ "pid":12345, "tid":5, "ts":1748104828391284, "dur":58, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.UnityConnectModule.dll" }}
,{ "pid":12345, "tid":5, "ts":1748104828391282, "dur":63, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UnityConnectModule.dll_B8C3EBBE45B8F7D8.mvfrm" }}
,{ "pid":12345, "tid":5, "ts":1748104828391346, "dur":81, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1748104828391438, "dur":52, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ClusterRendererModule.dll" }}
,{ "pid":12345, "tid":5, "ts":1748104828391435, "dur":58, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterRendererModule.dll_4DDA98497B51CAC3.mvfrm" }}
,{ "pid":12345, "tid":5, "ts":1748104828391551, "dur":51, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GridModule.dll_39C2A0AF39043944.mvfrm" }}
,{ "pid":12345, "tid":5, "ts":1748104828391702, "dur":222, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1748104828391940, "dur":86, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ProfilerModule.dll" }}
,{ "pid":12345, "tid":5, "ts":1748104828391938, "dur":92, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ProfilerModule.dll_584CD8E28CA163B7.mvfrm" }}
,{ "pid":12345, "tid":5, "ts":1748104828392031, "dur":50, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1748104828392095, "dur":231, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll.mvfrm.rsp" }}
,{ "pid":12345, "tid":5, "ts":1748104828392327, "dur":55, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1748104828392395, "dur":166, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll.mvfrm" }}
,{ "pid":12345, "tid":5, "ts":1748104828392562, "dur":72, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1748104828392885, "dur":106, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\mscorlib.dll" }}
,{ "pid":12345, "tid":5, "ts":1748104828392993, "dur":83, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.ComponentModel.Composition.dll" }}
,{ "pid":12345, "tid":5, "ts":1748104828393078, "dur":158, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Core.dll" }}
,{ "pid":12345, "tid":5, "ts":1748104828393282, "dur":74, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Drawing.dll" }}
,{ "pid":12345, "tid":5, "ts":1748104828393358, "dur":53, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.IO.Compression.FileSystem.dll" }}
,{ "pid":12345, "tid":5, "ts":1748104828393414, "dur":100, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Net.dll" }}
,{ "pid":12345, "tid":5, "ts":1748104828393516, "dur":77, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Numerics.dll" }}
,{ "pid":12345, "tid":5, "ts":1748104828393596, "dur":106, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Runtime.Serialization.dll" }}
,{ "pid":12345, "tid":5, "ts":1748104828393704, "dur":81, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.ServiceModel.Web.dll" }}
,{ "pid":12345, "tid":5, "ts":1748104828393787, "dur":75, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Transactions.dll" }}
,{ "pid":12345, "tid":5, "ts":1748104828393864, "dur":70, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Web.dll" }}
,{ "pid":12345, "tid":5, "ts":1748104828393936, "dur":76, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Windows.dll" }}
,{ "pid":12345, "tid":5, "ts":1748104828394014, "dur":75, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Xml.dll" }}
,{ "pid":12345, "tid":5, "ts":1748104828394091, "dur":54, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Xml.Linq.dll" }}
,{ "pid":12345, "tid":5, "ts":1748104828394147, "dur":80, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Xml.Serialization.dll" }}
,{ "pid":12345, "tid":5, "ts":1748104828394229, "dur":115, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\Microsoft.Win32.Primitives.dll" }}
,{ "pid":12345, "tid":5, "ts":1748104828394347, "dur":67, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.AppContext.dll" }}
,{ "pid":12345, "tid":5, "ts":1748104828394430, "dur":63, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Buffers.dll" }}
,{ "pid":12345, "tid":5, "ts":1748104828394496, "dur":94, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Collections.Concurrent.dll" }}
,{ "pid":12345, "tid":5, "ts":1748104828394593, "dur":86, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Collections.dll" }}
,{ "pid":12345, "tid":5, "ts":1748104828394681, "dur":108, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Collections.NonGeneric.dll" }}
,{ "pid":12345, "tid":5, "ts":1748104828394792, "dur":60, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Collections.Specialized.dll" }}
,{ "pid":12345, "tid":5, "ts":1748104828394855, "dur":78, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.ComponentModel.dll" }}
,{ "pid":12345, "tid":5, "ts":1748104828394936, "dur":75, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.ComponentModel.EventBasedAsync.dll" }}
,{ "pid":12345, "tid":5, "ts":1748104828395014, "dur":96, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.ComponentModel.Primitives.dll" }}
,{ "pid":12345, "tid":5, "ts":1748104828395113, "dur":69, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.ComponentModel.TypeConverter.dll" }}
,{ "pid":12345, "tid":5, "ts":1748104828395184, "dur":97, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Console.dll" }}
,{ "pid":12345, "tid":5, "ts":1748104828395284, "dur":127, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Data.Common.dll" }}
,{ "pid":12345, "tid":5, "ts":1748104828395413, "dur":55, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.Contracts.dll" }}
,{ "pid":12345, "tid":5, "ts":1748104828395470, "dur":86, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.Debug.dll" }}
,{ "pid":12345, "tid":5, "ts":1748104828395558, "dur":161, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.FileVersionInfo.dll" }}
,{ "pid":12345, "tid":5, "ts":1748104828395721, "dur":69, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.Process.dll" }}
,{ "pid":12345, "tid":5, "ts":1748104828395793, "dur":77, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.StackTrace.dll" }}
,{ "pid":12345, "tid":5, "ts":1748104828395873, "dur":110, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.TextWriterTraceListener.dll" }}
,{ "pid":12345, "tid":5, "ts":1748104828395985, "dur":140, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.Tools.dll" }}
,{ "pid":12345, "tid":5, "ts":1748104828396128, "dur":76, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.TraceSource.dll" }}
,{ "pid":12345, "tid":5, "ts":1748104828396570, "dur":269, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.FileSystem.dll" }}
,{ "pid":12345, "tid":5, "ts":1748104828396840, "dur":66, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.FileSystem.DriveInfo.dll" }}
,{ "pid":12345, "tid":5, "ts":1748104828396909, "dur":102, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.FileSystem.Primitives.dll" }}
,{ "pid":12345, "tid":5, "ts":1748104828397102, "dur":169, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.MemoryMappedFiles.dll" }}
,{ "pid":12345, "tid":5, "ts":1748104828397273, "dur":52, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.Pipes.dll" }}
,{ "pid":12345, "tid":5, "ts":1748104828397328, "dur":280, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.UnmanagedMemoryStream.dll" }}
,{ "pid":12345, "tid":5, "ts":1748104828397611, "dur":217, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Linq.dll" }}
,{ "pid":12345, "tid":5, "ts":1748104828397830, "dur":709, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Linq.Expressions.dll" }}
,{ "pid":12345, "tid":5, "ts":1748104828398541, "dur":85, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Linq.Parallel.dll" }}
,{ "pid":12345, "tid":5, "ts":1748104828398632, "dur":71, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Linq.Queryable.dll" }}
,{ "pid":12345, "tid":5, "ts":1748104828398705, "dur":78, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Memory.dll" }}
,{ "pid":12345, "tid":5, "ts":1748104828398786, "dur":60, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.Http.dll" }}
,{ "pid":12345, "tid":5, "ts":1748104828398848, "dur":93, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.NameResolution.dll" }}
,{ "pid":12345, "tid":5, "ts":1748104828399170, "dur":62, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.Sockets.dll" }}
,{ "pid":12345, "tid":5, "ts":1748104828399333, "dur":86, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Numerics.Vectors.dll" }}
,{ "pid":12345, "tid":5, "ts":1748104828399470, "dur":79, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Reflection.DispatchProxy.dll" }}
,{ "pid":12345, "tid":5, "ts":1748104828399738, "dur":88, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Reflection.Extensions.dll" }}
,{ "pid":12345, "tid":5, "ts":1748104828399828, "dur":64, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Reflection.Primitives.dll" }}
,{ "pid":12345, "tid":5, "ts":1748104828400027, "dur":71, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.CompilerServices.VisualC.dll" }}
,{ "pid":12345, "tid":5, "ts":1748104828400100, "dur":58, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.dll" }}
,{ "pid":12345, "tid":5, "ts":1748104828400161, "dur":133, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.Extensions.dll" }}
,{ "pid":12345, "tid":5, "ts":1748104828400296, "dur":174, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.Handles.dll" }}
,{ "pid":12345, "tid":5, "ts":1748104828400641, "dur":67, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.Serialization.Primitives.dll" }}
,{ "pid":12345, "tid":5, "ts":1748104828400709, "dur":72, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.Serialization.Xml.dll" }}
,{ "pid":12345, "tid":5, "ts":1748104828400784, "dur":64, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Security.Claims.dll" }}
,{ "pid":12345, "tid":5, "ts":1748104828400851, "dur":99, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Security.Cryptography.Algorithms.dll" }}
,{ "pid":12345, "tid":5, "ts":1748104828400953, "dur":61, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Security.Cryptography.Csp.dll" }}
,{ "pid":12345, "tid":5, "ts":1748104828401017, "dur":55, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Security.Cryptography.Encoding.dll" }}
,{ "pid":12345, "tid":5, "ts":1748104828401074, "dur":64, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Security.Cryptography.Primitives.dll" }}
,{ "pid":12345, "tid":5, "ts":1748104828401141, "dur":146, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Security.Cryptography.X509Certificates.dll" }}
,{ "pid":12345, "tid":5, "ts":1748104828401290, "dur":68, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Security.Principal.dll" }}
,{ "pid":12345, "tid":5, "ts":1748104828401360, "dur":71, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Security.SecureString.dll" }}
,{ "pid":12345, "tid":5, "ts":1748104828401482, "dur":66, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Text.Encoding.Extensions.dll" }}
,{ "pid":12345, "tid":5, "ts":1748104828401553, "dur":55, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Text.RegularExpressions.dll" }}
,{ "pid":12345, "tid":5, "ts":1748104828401610, "dur":68, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Threading.dll" }}
,{ "pid":12345, "tid":5, "ts":1748104828401681, "dur":92, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Threading.Overlapped.dll" }}
,{ "pid":12345, "tid":5, "ts":1748104828401776, "dur":453, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Threading.Tasks.dll" }}
,{ "pid":12345, "tid":5, "ts":1748104828402257, "dur":100, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Xml.XmlDocument.dll" }}
,{ "pid":12345, "tid":5, "ts":1748104828402359, "dur":56, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Xml.XmlSerializer.dll" }}
,{ "pid":12345, "tid":5, "ts":1748104828402418, "dur":72, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Xml.XPath.dll" }}
,{ "pid":12345, "tid":5, "ts":1748104828402493, "dur":112, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Xml.XPath.XDocument.dll" }}
,{ "pid":12345, "tid":5, "ts":1748104828402613, "dur":84, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\NetStandard\\ref\\2.1.0\\netstandard.dll" }}
,{ "pid":12345, "tid":5, "ts":1748104828402715, "dur":62, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\PackageCache\\com.gamelovers.mcp-unity@1aaca031af\\Editor\\Lib\\websocket-sharp.dll" }}
,{ "pid":12345, "tid":5, "ts":1748104828402787, "dur":53, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\PackageCache\\com.unity.collab-proxy@2.5.1\\Lib\\Editor\\PlasticSCM\\log4netPlastic.dll" }}
,{ "pid":12345, "tid":5, "ts":1748104828402952, "dur":53, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\PackageCache\\com.unity.shadergraph@14.0.11\\Editor\\Generation\\Targets\\BuiltIn\\Editor\\ShaderGUI\\MaterialAssemblyReference\\RawRenderQueue.cs" }}
,{ "pid":12345, "tid":5, "ts":1748104828403203, "dur":63, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\EventSystem\\EventInterfaces.cs" }}
,{ "pid":12345, "tid":5, "ts":1748104828403268, "dur":54, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\EventSystem\\EventSystem.cs" }}
,{ "pid":12345, "tid":5, "ts":1748104828403324, "dur":50, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\EventSystem\\EventTrigger.cs" }}
,{ "pid":12345, "tid":5, "ts":1748104828403618, "dur":50, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\EventSystem\\InputModules\\StandaloneInputModule.cs" }}
,{ "pid":12345, "tid":5, "ts":1748104828403670, "dur":53, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\EventSystem\\InputModules\\TouchInputModule.cs" }}
,{ "pid":12345, "tid":5, "ts":1748104828403725, "dur":54, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\EventSystem\\MoveDirection.cs" }}
,{ "pid":12345, "tid":5, "ts":1748104828403781, "dur":56, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\EventSystem\\RaycasterManager.cs" }}
,{ "pid":12345, "tid":5, "ts":1748104828403839, "dur":52, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\EventSystem\\Raycasters\\BaseRaycaster.cs" }}
,{ "pid":12345, "tid":5, "ts":1748104828403944, "dur":52, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\EventSystem\\Raycasters\\PhysicsRaycaster.cs" }}
,{ "pid":12345, "tid":5, "ts":1748104828404045, "dur":121, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\EventSystem\\UIBehaviour.cs" }}
,{ "pid":12345, "tid":5, "ts":1748104828404263, "dur":53, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\Properties\\AssemblyInfo.cs" }}
,{ "pid":12345, "tid":5, "ts":1748104828404367, "dur":346, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\AnimationTriggers.cs" }}
,{ "pid":12345, "tid":5, "ts":1748104828404814, "dur":297, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\ColorBlock.cs" }}
,{ "pid":12345, "tid":5, "ts":1748104828405113, "dur":58, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\Culling\\ClipperRegistry.cs" }}
,{ "pid":12345, "tid":5, "ts":1748104828405453, "dur":94, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\FontUpdateTracker.cs" }}
,{ "pid":12345, "tid":5, "ts":1748104828405591, "dur":50, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\GraphicRaycaster.cs" }}
,{ "pid":12345, "tid":5, "ts":1748104828405693, "dur":54, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\GraphicRegistry.cs" }}
,{ "pid":12345, "tid":5, "ts":1748104828405846, "dur":57, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\IMask.cs" }}
,{ "pid":12345, "tid":5, "ts":1748104828406080, "dur":798, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\Layout\\ContentSizeFitter.cs" }}
,{ "pid":12345, "tid":5, "ts":1748104828406880, "dur":153, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\Layout\\GridLayoutGroup.cs" }}
,{ "pid":12345, "tid":5, "ts":1748104828407035, "dur":53, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\Layout\\HorizontalLayoutGroup.cs" }}
,{ "pid":12345, "tid":5, "ts":1748104828407090, "dur":284, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\Layout\\HorizontalOrVerticalLayoutGroup.cs" }}
,{ "pid":12345, "tid":5, "ts":1748104828407377, "dur":56, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\Layout\\ILayoutElement.cs" }}
,{ "pid":12345, "tid":5, "ts":1748104828407435, "dur":53, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\Layout\\LayoutElement.cs" }}
,{ "pid":12345, "tid":5, "ts":1748104828407490, "dur":50, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\Layout\\LayoutGroup.cs" }}
,{ "pid":12345, "tid":5, "ts":1748104828407542, "dur":57, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\Layout\\LayoutRebuilder.cs" }}
,{ "pid":12345, "tid":5, "ts":1748104828407601, "dur":50, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\Layout\\LayoutUtility.cs" }}
,{ "pid":12345, "tid":5, "ts":1748104828407699, "dur":72, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\Mask.cs" }}
,{ "pid":12345, "tid":5, "ts":1748104828407778, "dur":56, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\MaskableGraphic.cs" }}
,{ "pid":12345, "tid":5, "ts":1748104828407835, "dur":55, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\MaskUtilities.cs" }}
,{ "pid":12345, "tid":5, "ts":1748104828407892, "dur":58, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\MaterialModifiers\\IMaterialModifier.cs" }}
,{ "pid":12345, "tid":5, "ts":1748104828408046, "dur":50, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\Navigation.cs" }}
,{ "pid":12345, "tid":5, "ts":1748104828408098, "dur":158, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\RawImage.cs" }}
,{ "pid":12345, "tid":5, "ts":1748104828408258, "dur":52, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\RectMask2D.cs" }}
,{ "pid":12345, "tid":5, "ts":1748104828408358, "dur":772, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\ScrollRect.cs" }}
,{ "pid":12345, "tid":5, "ts":1748104828409133, "dur":65, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\Selectable.cs" }}
,{ "pid":12345, "tid":5, "ts":1748104828409247, "dur":51, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\Slider.cs" }}
,{ "pid":12345, "tid":5, "ts":1748104828409866, "dur":169, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\Tools\\Unity.SourceGenerators\\Unity.Properties.SourceGenerator.dll" }}
,{ "pid":12345, "tid":5, "ts":1748104828392646, "dur":17439, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll (+2 others)" }}
,{ "pid":12345, "tid":5, "ts":1748104828410086, "dur":123, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1748104828410209, "dur":261, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll (+2 others)" }}
,{ "pid":12345, "tid":5, "ts":1748104828410486, "dur":82, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1748104828410584, "dur":1877, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1748104828412462, "dur":1172, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.render-pipelines.core@14.0.11\\Runtime\\PostProcessing\\LensFlareComponentSRP.cs" }}
,{ "pid":12345, "tid":5, "ts":1748104828412462, "dur":2445, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1748104828414908, "dur":3138, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1748104828418047, "dur":1578, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1748104828419625, "dur":1871, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1748104828421502, "dur":117, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1748104828421627, "dur":764, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1748104828422391, "dur":91, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1748104828422486, "dur":168, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.dll.mvfrm" }}
,{ "pid":12345, "tid":5, "ts":1748104828422654, "dur":62, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1748104828422716, "dur":96, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.dll.mvfrm" }}
,{ "pid":12345, "tid":5, "ts":1748104828423285, "dur":130, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Runtime.CompilerServices.VisualC.dll" }}
,{ "pid":12345, "tid":5, "ts":1748104828422815, "dur":880, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.dll (+2 others)" }}
,{ "pid":12345, "tid":5, "ts":1748104828423696, "dur":518, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1748104828424227, "dur":55, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1748104828424290, "dur":234, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Sprite.Editor.dll.mvfrm" }}
,{ "pid":12345, "tid":5, "ts":1748104828424524, "dur":90, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1748104828424622, "dur":841, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Sprite.Editor.dll (+2 others)" }}
,{ "pid":12345, "tid":5, "ts":1748104828425464, "dur":613, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1748104828426086, "dur":323, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Services.Core.Threading.dll.mvfrm" }}
,{ "pid":12345, "tid":5, "ts":1748104828426410, "dur":55, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1748104828426504, "dur":310, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Services.Core.Environments.Internal.dll.mvfrm" }}
,{ "pid":12345, "tid":5, "ts":1748104828426815, "dur":52, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1748104828426934, "dur":414, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.UIElementsSamplesModule.dll" }}
,{ "pid":12345, "tid":5, "ts":1748104828427365, "dur":101, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ARModule.dll" }}
,{ "pid":12345, "tid":5, "ts":1748104828427660, "dur":71, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Data.dll" }}
,{ "pid":12345, "tid":5, "ts":1748104828427768, "dur":99, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.ServiceModel.Web.dll" }}
,{ "pid":12345, "tid":5, "ts":1748104828426880, "dur":1403, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Services.Core.Environments.Internal.dll (+2 others)" }}
,{ "pid":12345, "tid":5, "ts":1748104828428284, "dur":300, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1748104828428585, "dur":58, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Services.Core.Environments.Internal.dll (+2 others)" }}
,{ "pid":12345, "tid":5, "ts":1748104828428656, "dur":68, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1748104828428732, "dur":322, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Services.Core.Registration.dll.mvfrm" }}
,{ "pid":12345, "tid":5, "ts":1748104828429055, "dur":153, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1748104828429381, "dur":111, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityConnectModule.dll" }}
,{ "pid":12345, "tid":5, "ts":1748104828429217, "dur":882, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Services.Core.Registration.dll (+2 others)" }}
,{ "pid":12345, "tid":5, "ts":1748104828430100, "dur":568, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1748104828430689, "dur":69, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1748104828430759, "dur":64, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Services.Core.Registration.ref.dll_0EE1D1878B496356.mvfrm" }}
,{ "pid":12345, "tid":5, "ts":1748104828430826, "dur":1332, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Services.Core.Editor.dll.mvfrm" }}
,{ "pid":12345, "tid":5, "ts":1748104828432164, "dur":50, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1748104828432226, "dur":1026, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Services.Core.Editor.dll (+2 others)" }}
,{ "pid":12345, "tid":5, "ts":1748104828433253, "dur":244, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1748104828433596, "dur":96, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1748104828433789, "dur":179, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Services.Analytics.Editor.dll.mvfrm" }}
,{ "pid":12345, "tid":5, "ts":1748104828433969, "dur":63, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1748104828434040, "dur":552, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Services.Analytics.Editor.dll (+2 others)" }}
,{ "pid":12345, "tid":5, "ts":1748104828434594, "dur":159, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1748104828434805, "dur":58, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1748104828434882, "dur":216, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll.mvfrm" }}
,{ "pid":12345, "tid":5, "ts":1748104828435099, "dur":61, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1748104828435167, "dur":646, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll (+2 others)" }}
,{ "pid":12345, "tid":5, "ts":1748104828435820, "dur":74, "ph":"X", "name": "StoreTimestampsOfNonGeneratedInputFiles",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1748104828436871, "dur":552463, "ph":"X", "name": "Csc",  "args": { "detail":"Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll (+2 others)" }}
,{ "pid":12345, "tid":6, "ts":1748104828358474, "dur":31687, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1748104828390187, "dur":282, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityWebRequestAssetBundleModule.dll" }}
,{ "pid":12345, "tid":6, "ts":1748104828390471, "dur":571, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe" }}
,{ "pid":12345, "tid":6, "ts":1748104828390167, "dur":876, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAssetBundleModule.dll_FFE57DA3D23D9CA0.mvfrm" }}
,{ "pid":12345, "tid":6, "ts":1748104828391044, "dur":64, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1748104828391119, "dur":59, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.DiagnosticsModule.dll" }}
,{ "pid":12345, "tid":6, "ts":1748104828391116, "dur":65, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DiagnosticsModule.dll_F7487C2004B307CD.mvfrm" }}
,{ "pid":12345, "tid":6, "ts":1748104828391242, "dur":176, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.TextCoreTextEngineModule.dll" }}
,{ "pid":12345, "tid":6, "ts":1748104828391240, "dur":181, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreTextEngineModule.dll_745B4A523497F47E.mvfrm" }}
,{ "pid":12345, "tid":6, "ts":1748104828391422, "dur":53, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1748104828391483, "dur":52, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CrashReportingModule.dll_0CF47EE53450C905.mvfrm" }}
,{ "pid":12345, "tid":6, "ts":1748104828391536, "dur":52, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1748104828391699, "dur":55, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ParticleSystemModule.dll" }}
,{ "pid":12345, "tid":6, "ts":1748104828391696, "dur":61, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ParticleSystemModule.dll_6006C831A2625493.mvfrm" }}
,{ "pid":12345, "tid":6, "ts":1748104828391808, "dur":168, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\PlaybackEngines\\AndroidPlayer\\Unity.Android.GradleProject.dll" }}
,{ "pid":12345, "tid":6, "ts":1748104828391806, "dur":173, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Android.GradleProject.dll_1D33C865C4B5BB4B.mvfrm" }}
,{ "pid":12345, "tid":6, "ts":1748104828392034, "dur":204, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.UnityAdditionalFile.txt" }}
,{ "pid":12345, "tid":6, "ts":1748104828392239, "dur":160, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1748104828392409, "dur":63, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1748104828392478, "dur":197, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll.mvfrm.rsp" }}
,{ "pid":12345, "tid":6, "ts":1748104828392676, "dur":57, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1748104828392740, "dur":53, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1748104828392799, "dur":321, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteText Library/Bee/artifacts/1900b0aE.dag/GLTFSerialization.rsp" }}
,{ "pid":12345, "tid":6, "ts":1748104828393121, "dur":51, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1748104828393172, "dur":145, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/1900b0aE.dag/GLTFSerialization.rsp" }}
,{ "pid":12345, "tid":6, "ts":1748104828393334, "dur":50, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1748104828393390, "dur":257, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.rsp" }}
,{ "pid":12345, "tid":6, "ts":1748104828393735, "dur":170, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Formats.Fbx.Runtime.rsp2" }}
,{ "pid":12345, "tid":6, "ts":1748104828393906, "dur":54, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1748104828393966, "dur":375, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteText Library/Bee/artifacts/1900b0aE.dag/UnityGLTFEditor.rsp" }}
,{ "pid":12345, "tid":6, "ts":1748104828394342, "dur":62, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1748104828394405, "dur":425, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/1900b0aE.dag/UnityGLTFEditor.rsp" }}
,{ "pid":12345, "tid":6, "ts":1748104828394833, "dur":56, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1748104828394889, "dur":200, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.rsp2" }}
,{ "pid":12345, "tid":6, "ts":1748104828395147, "dur":50, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1748104828395325, "dur":378, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Analytics.DataPrivacy.rsp" }}
,{ "pid":12345, "tid":6, "ts":1748104828395909, "dur":109, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.Advertisements.DevX.Editor.UnityAdditionalFile.txt" }}
,{ "pid":12345, "tid":6, "ts":1748104828396020, "dur":53, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteText Library/Bee/artifacts/1900b0aE.dag/UnityGLTF.Plugins.rsp" }}
,{ "pid":12345, "tid":6, "ts":1748104828396123, "dur":1186, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1748104828397309, "dur":2214, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1748104828399524, "dur":858, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1748104828401337, "dur":574, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.2d.tilemap@1.0.0\\Editor\\UI\\GridPaintPaletteWindowSplitView.cs" }}
,{ "pid":12345, "tid":6, "ts":1748104828400383, "dur":3122, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1748104828403506, "dur":1000, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1748104828404506, "dur":1252, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1748104828405758, "dur":891, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1748104828406650, "dur":1170, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1748104828408386, "dur":768, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\org.khronos.unitygltf@1d3d93121c\\Runtime\\Plugins\\GLTFSerialization\\Schema\\AccessorSparse.cs" }}
,{ "pid":12345, "tid":6, "ts":1748104828407821, "dur":1642, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1748104828409464, "dur":912, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1748104828410381, "dur":154, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1748104828410545, "dur":90, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1748104828410643, "dur":1293, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1748104828411936, "dur":1052, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1748104828412988, "dur":1681, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1748104828415005, "dur":1152, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.collab-proxy@2.5.1\\Editor\\PlasticSCM\\Configuration\\ToolConfig.cs" }}
,{ "pid":12345, "tid":6, "ts":1748104828418280, "dur":1509, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.collab-proxy@2.5.1\\Editor\\PlasticSCM\\Configuration\\ChannelCertificateUiImpl.cs" }}
,{ "pid":12345, "tid":6, "ts":1748104828414669, "dur":5471, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1748104828420140, "dur":1502, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1748104828421643, "dur":714, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1748104828422358, "dur":92, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1748104828422454, "dur":180, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll.mvfrm" }}
,{ "pid":12345, "tid":6, "ts":1748104828422639, "dur":74, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1748104828422713, "dur":110, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll.mvfrm" }}
,{ "pid":12345, "tid":6, "ts":1748104828422985, "dur":53, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.TilemapModule.dll" }}
,{ "pid":12345, "tid":6, "ts":1748104828423283, "dur":148, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.WebSockets.dll" }}
,{ "pid":12345, "tid":6, "ts":1748104828422825, "dur":1109, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll (+2 others)" }}
,{ "pid":12345, "tid":6, "ts":1748104828423935, "dur":536, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1748104828424484, "dur":55, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1748104828424546, "dur":243, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/GLTFSerialization.dll.mvfrm" }}
,{ "pid":12345, "tid":6, "ts":1748104828425020, "dur":459, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.ComponentModel.Composition.dll" }}
,{ "pid":12345, "tid":6, "ts":1748104828425807, "dur":474, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.FileSystem.dll" }}
,{ "pid":12345, "tid":6, "ts":1748104828426565, "dur":377, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\PackageCache\\org.khronos.unitygltf@1d3d93121c\\Runtime\\Plugins\\GLTFSerialization\\Extensions\\KHR_materials_unlitFactory.cs" }}
,{ "pid":12345, "tid":6, "ts":1748104828424840, "dur":2382, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1900b0aE.dag/GLTFSerialization.dll (+2 others)" }}
,{ "pid":12345, "tid":6, "ts":1748104828427223, "dur":128, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1748104828427364, "dur":51, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEditor.Graphs.dll" }}
,{ "pid":12345, "tid":6, "ts":1748104828427661, "dur":115, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Transactions.dll" }}
,{ "pid":12345, "tid":6, "ts":1748104828427362, "dur":1177, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1900b0aE.dag/UnityGLTFScripts.dll (+2 others)" }}
,{ "pid":12345, "tid":6, "ts":1748104828428540, "dur":432, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1748104828429037, "dur":197, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityGLTFEditor.dll.mvfrm" }}
,{ "pid":12345, "tid":6, "ts":1748104828429235, "dur":50, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1748104828429292, "dur":701, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1900b0aE.dag/UnityGLTFEditor.dll (+2 others)" }}
,{ "pid":12345, "tid":6, "ts":1748104828429994, "dur":132, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1748104828430157, "dur":71, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1748104828430236, "dur":225, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityGLTF.Interactivity.VisualScriptingInstall.dll.mvfrm" }}
,{ "pid":12345, "tid":6, "ts":1748104828430462, "dur":73, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1748104828430543, "dur":692, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1900b0aE.dag/UnityGLTF.Interactivity.VisualScriptingInstall.dll (+2 others)" }}
,{ "pid":12345, "tid":6, "ts":1748104828431236, "dur":166, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1748104828431471, "dur":160, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1748104828431656, "dur":89, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1748104828431753, "dur":341, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1748104828432104, "dur":88, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1748104828432201, "dur":104, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1748104828432316, "dur":192, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1748104828432519, "dur":1201, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1748104828433720, "dur":124, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1748104828433844, "dur":958, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1748104828434802, "dur":87, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1748104828434891, "dur":223, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.dll.mvfrm" }}
,{ "pid":12345, "tid":6, "ts":1748104828435115, "dur":66, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1748104828435189, "dur":96695, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1748104828358510, "dur":31669, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1748104828390205, "dur":253, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityTestProtocolModule.dll" }}
,{ "pid":12345, "tid":7, "ts":1748104828390460, "dur":812, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe" }}
,{ "pid":12345, "tid":7, "ts":1748104828390187, "dur":1086, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityTestProtocolModule.dll_C39DF56EB3CCDA85.mvfrm" }}
,{ "pid":12345, "tid":7, "ts":1748104828391323, "dur":334, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.AndroidJNIModule.dll" }}
,{ "pid":12345, "tid":7, "ts":1748104828391321, "dur":340, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AndroidJNIModule.dll_8FABE4982BFB36F4.mvfrm" }}
,{ "pid":12345, "tid":7, "ts":1748104828391661, "dur":89, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1748104828391765, "dur":168, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.XRModule.dll" }}
,{ "pid":12345, "tid":7, "ts":1748104828391763, "dur":175, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.XRModule.dll_BEEF583D6FF32EB8.mvfrm" }}
,{ "pid":12345, "tid":7, "ts":1748104828391938, "dur":51, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1748104828391999, "dur":78, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\PackageCache\\com.unity.collab-proxy@2.5.1\\Lib\\Editor\\PlasticSCM\\Unity.Plastic.Newtonsoft.Json.dll" }}
,{ "pid":12345, "tid":7, "ts":1748104828391997, "dur":83, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Plastic.Newtonsoft.Json.dll_80F2FE2EFD1109AE.mvfrm" }}
,{ "pid":12345, "tid":7, "ts":1748104828392081, "dur":52, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1748104828392158, "dur":521, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.rsp" }}
,{ "pid":12345, "tid":7, "ts":1748104828392680, "dur":65, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1748104828392774, "dur":162, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteText Library/Bee/artifacts/1900b0aE.dag/UnityGLTFScripts.UnityAdditionalFile.txt" }}
,{ "pid":12345, "tid":7, "ts":1748104828392991, "dur":158, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.SpatialTracking.UnityAdditionalFile.txt" }}
,{ "pid":12345, "tid":7, "ts":1748104828393150, "dur":59, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1748104828393209, "dur":122, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.SpatialTracking.UnityAdditionalFile.txt" }}
,{ "pid":12345, "tid":7, "ts":1748104828393351, "dur":84, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Services.Core.Configuration.Editor.rsp2" }}
,{ "pid":12345, "tid":7, "ts":1748104828393437, "dur":60, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1748104828393506, "dur":54, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1748104828393577, "dur":51, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1748104828393646, "dur":52, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1748104828393699, "dur":52, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.rsp2" }}
,{ "pid":12345, "tid":7, "ts":1748104828393755, "dur":57, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1748104828393844, "dur":126, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.dll.mvfrm.rsp" }}
,{ "pid":12345, "tid":7, "ts":1748104828393971, "dur":51, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1748104828394135, "dur":190, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.rsp" }}
,{ "pid":12345, "tid":7, "ts":1748104828394326, "dur":53, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1748104828394380, "dur":196, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.rsp" }}
,{ "pid":12345, "tid":7, "ts":1748104828394741, "dur":52, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1748104828394806, "dur":117, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1748104828394923, "dur":250, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.rsp2" }}
,{ "pid":12345, "tid":7, "ts":1748104828395186, "dur":73, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1748104828395300, "dur":52, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1748104828395357, "dur":300, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.Advertisements.Editor.rsp" }}
,{ "pid":12345, "tid":7, "ts":1748104828395734, "dur":64, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteText Library/Bee/artifacts/1900b0aE.dag/Autodesk.Fbx.Editor.rsp" }}
,{ "pid":12345, "tid":7, "ts":1748104828395799, "dur":52, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1748104828395913, "dur":129, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/1900b0aE.dag/FbxBuildTestAssets.dll.mvfrm.rsp" }}
,{ "pid":12345, "tid":7, "ts":1748104828396044, "dur":156, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.rsp" }}
,{ "pid":12345, "tid":7, "ts":1748104828396201, "dur":92, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1748104828396294, "dur":447, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.rsp" }}
,{ "pid":12345, "tid":7, "ts":1748104828396742, "dur":1469, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1748104828399295, "dur":1424, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.formats.fbx@4.2.1\\Editor\\ExportModelSettings.cs" }}
,{ "pid":12345, "tid":7, "ts":1748104828400878, "dur":673, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.formats.fbx@4.2.1\\Editor\\CustomExtensions.cs" }}
,{ "pid":12345, "tid":7, "ts":1748104828398212, "dur":4385, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1748104828402598, "dur":946, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1748104828403545, "dur":976, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1748104828404522, "dur":827, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1748104828405350, "dur":960, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1748104828406311, "dur":1110, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1748104828407422, "dur":834, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1748104828408385, "dur":1060, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\org.khronos.unitygltf@1d3d93121c\\Runtime\\Plugins\\GLTFSerialization\\Extensions\\KHR_materials_pbrSpecularGlossinessFactory.cs" }}
,{ "pid":12345, "tid":7, "ts":1748104828408257, "dur":1925, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1748104828410182, "dur":1036, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1748104828411219, "dur":883, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1748104828412102, "dur":1149, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.render-pipelines.core@14.0.11\\Runtime\\Volume\\VolumeParameter.cs" }}
,{ "pid":12345, "tid":7, "ts":1748104828412102, "dur":2750, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1748104828414853, "dur":2577, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1748104828417431, "dur":918, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1748104828418407, "dur":574, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEditor.TestRunner\\GUI\\Views\\TestListGUIBase.cs" }}
,{ "pid":12345, "tid":7, "ts":1748104828418349, "dur":1585, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1748104828419935, "dur":1564, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1748104828421500, "dur":848, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1748104828422354, "dur":143, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1748104828422498, "dur":178, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.dll.mvfrm" }}
,{ "pid":12345, "tid":7, "ts":1748104828422677, "dur":72, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1748104828422797, "dur":617, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.UIElementsModule.dll" }}
,{ "pid":12345, "tid":7, "ts":1748104828422762, "dur":1299, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.dll (+2 others)" }}
,{ "pid":12345, "tid":7, "ts":1748104828424062, "dur":294, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1748104828424373, "dur":57, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1748104828424435, "dur":267, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Tilemap.Editor.dll.mvfrm" }}
,{ "pid":12345, "tid":7, "ts":1748104828424703, "dur":587, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1748104828425472, "dur":342, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.LocalizationModule.dll" }}
,{ "pid":12345, "tid":7, "ts":1748104828425297, "dur":1349, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Tilemap.Editor.dll (+2 others)" }}
,{ "pid":12345, "tid":7, "ts":1748104828426648, "dur":802, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1748104828427667, "dur":113, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityTestProtocolModule.dll" }}
,{ "pid":12345, "tid":7, "ts":1748104828427962, "dur":107, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.TraceSource.dll" }}
,{ "pid":12345, "tid":7, "ts":1748104828427469, "dur":920, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Analytics.DataPrivacy.dll (+2 others)" }}
,{ "pid":12345, "tid":7, "ts":1748104828428390, "dur":212, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1748104828428642, "dur":56, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1748104828428715, "dur":53, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1748104828429194, "dur":155, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityGLTF.Plugins.dll.mvfrm" }}
,{ "pid":12345, "tid":7, "ts":1748104828429350, "dur":261, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1748104828429618, "dur":853, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1900b0aE.dag/UnityGLTF.Plugins.dll (+2 others)" }}
,{ "pid":12345, "tid":7, "ts":1748104828430472, "dur":194, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1748104828430676, "dur":149, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1748104828430836, "dur":115, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1748104828430960, "dur":173, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1748104828431144, "dur":101, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1748104828431269, "dur":134, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1748104828431415, "dur":233, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1748104828431661, "dur":103, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1748104828431773, "dur":377, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1748104828432162, "dur":480, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1748104828432646, "dur":991, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1748104828433638, "dur":267, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1748104828433906, "dur":917, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1748104828434823, "dur":97035, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1748104828358571, "dur":31624, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1748104828390218, "dur":73, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityCurlModule.dll" }}
,{ "pid":12345, "tid":8, "ts":1748104828390293, "dur":427, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe" }}
,{ "pid":12345, "tid":8, "ts":1748104828390202, "dur":519, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityCurlModule.dll_394DBF90E02A7A97.mvfrm" }}
,{ "pid":12345, "tid":8, "ts":1748104828390724, "dur":310, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1748104828391045, "dur":52, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ScreenCaptureModule.dll" }}
,{ "pid":12345, "tid":8, "ts":1748104828391042, "dur":59, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ScreenCaptureModule.dll_780F813CA6C997F0.mvfrm" }}
,{ "pid":12345, "tid":8, "ts":1748104828391102, "dur":56, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1748104828391169, "dur":70, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.QuickSearchModule.dll" }}
,{ "pid":12345, "tid":8, "ts":1748104828391167, "dur":76, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.QuickSearchModule.dll_8257FD2D5D8A0426.mvfrm" }}
,{ "pid":12345, "tid":8, "ts":1748104828391244, "dur":57, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1748104828391312, "dur":57, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.AIModule.dll" }}
,{ "pid":12345, "tid":8, "ts":1748104828391310, "dur":62, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AIModule.dll_3DEAD085E8B75636.mvfrm" }}
,{ "pid":12345, "tid":8, "ts":1748104828391373, "dur":135, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1748104828391521, "dur":108, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.DSPGraphModule.dll" }}
,{ "pid":12345, "tid":8, "ts":1748104828391519, "dur":113, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DSPGraphModule.dll_2B58011565A8ADCB.mvfrm" }}
,{ "pid":12345, "tid":8, "ts":1748104828391633, "dur":59, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1748104828391705, "dur":55, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.PerformanceReportingModule.dll" }}
,{ "pid":12345, "tid":8, "ts":1748104828391702, "dur":62, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PerformanceReportingModule.dll_B2FD0A79FAF3C50C.mvfrm" }}
,{ "pid":12345, "tid":8, "ts":1748104828391765, "dur":168, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1748104828391943, "dur":75, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\PlaybackEngines\\AndroidPlayer\\UnityEditor.Android.Extensions.dll" }}
,{ "pid":12345, "tid":8, "ts":1748104828391941, "dur":81, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Android.Extensions.dll_AA5D2B6D0677898A.mvfrm" }}
,{ "pid":12345, "tid":8, "ts":1748104828392023, "dur":66, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1748104828392136, "dur":64, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.NVIDIAModule.dll" }}
,{ "pid":12345, "tid":8, "ts":1748104828392134, "dur":69, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.NVIDIAModule.dll_ABE55F390DAAF359.mvfrm" }}
,{ "pid":12345, "tid":8, "ts":1748104828392260, "dur":236, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Services.Core.UnityAdditionalFile.txt" }}
,{ "pid":12345, "tid":8, "ts":1748104828392498, "dur":63, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1748104828392569, "dur":164, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll.mvfrm.rsp" }}
,{ "pid":12345, "tid":8, "ts":1748104828392734, "dur":66, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1748104828392830, "dur":145, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Services.Core.Environments.dll.mvfrm.rsp" }}
,{ "pid":12345, "tid":8, "ts":1748104828392976, "dur":65, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1748104828393049, "dur":51, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1748104828393107, "dur":170, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.rsp2" }}
,{ "pid":12345, "tid":8, "ts":1748104828393278, "dur":57, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1748104828393349, "dur":199, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.dll.mvfrm.rsp" }}
,{ "pid":12345, "tid":8, "ts":1748104828393550, "dur":63, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1748104828393643, "dur":201, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Services.Core.Networking.rsp" }}
,{ "pid":12345, "tid":8, "ts":1748104828393845, "dur":51, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1748104828393903, "dur":389, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.rsp" }}
,{ "pid":12345, "tid":8, "ts":1748104828394293, "dur":62, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1748104828394356, "dur":203, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.rsp" }}
,{ "pid":12345, "tid":8, "ts":1748104828394623, "dur":54, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1748104828394733, "dur":55, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1748104828394795, "dur":152, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.rsp2" }}
,{ "pid":12345, "tid":8, "ts":1748104828394994, "dur":311, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.rsp2" }}
,{ "pid":12345, "tid":8, "ts":1748104828395346, "dur":64, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1748104828395415, "dur":252, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.rsp" }}
,{ "pid":12345, "tid":8, "ts":1748104828395716, "dur":126, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.rsp" }}
,{ "pid":12345, "tid":8, "ts":1748104828395845, "dur":53, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1748104828395914, "dur":167, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Services.Core.Environments.Editor.dll.mvfrm.rsp" }}
,{ "pid":12345, "tid":8, "ts":1748104828396137, "dur":1299, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1748104828398511, "dur":1941, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.ide.visualstudio@2.0.22\\Editor\\UsageUtility.cs" }}
,{ "pid":12345, "tid":8, "ts":1748104828397436, "dur":5486, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1748104828403672, "dur":1832, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.services.analytics@5.0.0\\Runtime\\StandardEventServiceComponent.cs" }}
,{ "pid":12345, "tid":8, "ts":1748104828402922, "dur":2632, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1748104828405555, "dur":1639, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.shadergraph@14.0.11\\Editor\\Data\\Nodes\\Math\\Trigonometry\\ArctangentNode.cs" }}
,{ "pid":12345, "tid":8, "ts":1748104828407194, "dur":2562, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.shadergraph@14.0.11\\Editor\\Data\\Nodes\\Math\\Trigonometry\\Arctangent2Node.cs" }}
,{ "pid":12345, "tid":8, "ts":1748104828405555, "dur":5007, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1748104828410563, "dur":1034, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1748104828411598, "dur":856, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1748104828412455, "dur":1015, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1748104828414808, "dur":518, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.collab-proxy@2.5.1\\Editor\\PlasticSCM\\Views\\Changesets\\LaunchDiffOperations.cs" }}
,{ "pid":12345, "tid":8, "ts":1748104828415631, "dur":872, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.collab-proxy@2.5.1\\Editor\\PlasticSCM\\Views\\Changesets\\ChangesetsTab_Operations.cs" }}
,{ "pid":12345, "tid":8, "ts":1748104828413471, "dur":4113, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1748104828418268, "dur":843, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEditor.TestRunner\\UnityTestProtocol\\IUtpMessageReporter.cs" }}
,{ "pid":12345, "tid":8, "ts":1748104828417663, "dur":1784, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1748104828419595, "dur":1906, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1748104828421502, "dur":842, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1748104828422354, "dur":464, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1748104828422829, "dur":509, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VSCode.Editor.dll.mvfrm" }}
,{ "pid":12345, "tid":8, "ts":1748104828423340, "dur":77, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1748104828423431, "dur":261, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Analytics.DataPrivacy.dll.mvfrm" }}
,{ "pid":12345, "tid":8, "ts":1748104828423754, "dur":359, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.Advertisements.dll.mvfrm" }}
,{ "pid":12345, "tid":8, "ts":1748104828424114, "dur":71, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1748104828424194, "dur":343, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.Advertisements.Editor.dll.mvfrm" }}
,{ "pid":12345, "tid":8, "ts":1748104828424538, "dur":113, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1748104828424662, "dur":753, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.Advertisements.Editor.dll (+2 others)" }}
,{ "pid":12345, "tid":8, "ts":1748104828425416, "dur":397, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1748104828425833, "dur":294, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Services.Core.Configuration.dll.mvfrm" }}
,{ "pid":12345, "tid":8, "ts":1748104828426128, "dur":171, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1748104828426649, "dur":67, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.FileSystem.dll" }}
,{ "pid":12345, "tid":8, "ts":1748104828426934, "dur":97, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Xml.XPath.XDocument.dll" }}
,{ "pid":12345, "tid":8, "ts":1748104828426310, "dur":808, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Services.Core.Configuration.dll (+2 others)" }}
,{ "pid":12345, "tid":8, "ts":1748104828427119, "dur":155, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1748104828427290, "dur":52, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1748104828427349, "dur":210, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Services.Core.Configuration.Editor.dll.mvfrm" }}
,{ "pid":12345, "tid":8, "ts":1748104828427560, "dur":57, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1748104828427659, "dur":62, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.TextCoreFontEngineModule.dll" }}
,{ "pid":12345, "tid":8, "ts":1748104828427630, "dur":814, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Services.Core.Configuration.Editor.dll (+2 others)" }}
,{ "pid":12345, "tid":8, "ts":1748104828428445, "dur":153, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1748104828428672, "dur":54, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1748104828428771, "dur":52, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1748104828429229, "dur":273, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/FbxBuildTestAssets.dll.mvfrm" }}
,{ "pid":12345, "tid":8, "ts":1748104828429601, "dur":882, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.UnityConnectModule.dll" }}
,{ "pid":12345, "tid":8, "ts":1748104828430660, "dur":52, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.StreamingModule.dll" }}
,{ "pid":12345, "tid":8, "ts":1748104828429556, "dur":1592, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1900b0aE.dag/FbxBuildTestAssets.dll (+2 others)" }}
,{ "pid":12345, "tid":8, "ts":1748104828431149, "dur":135, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1748104828431295, "dur":562, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1748104828431866, "dur":516, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1748104828432388, "dur":120, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1748104828432512, "dur":100, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1748104828432626, "dur":1052, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1748104828433679, "dur":109, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1748104828433790, "dur":303, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Services.Core.Environments.Editor.dll.mvfrm" }}
,{ "pid":12345, "tid":8, "ts":1748104828434095, "dur":73, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1748104828434182, "dur":643, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Services.Core.Environments.Editor.dll (+2 others)" }}
,{ "pid":12345, "tid":8, "ts":1748104828434826, "dur":139, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1748104828434980, "dur":216, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1748104828435202, "dur":96675, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1748104828358621, "dur":31597, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1748104828390247, "dur":280, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityConnectModule.dll" }}
,{ "pid":12345, "tid":9, "ts":1748104828390530, "dur":179, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe" }}
,{ "pid":12345, "tid":9, "ts":1748104828390227, "dur":483, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityConnectModule.dll_A6236FCD4040F883.mvfrm" }}
,{ "pid":12345, "tid":9, "ts":1748104828390740, "dur":204, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1748104828390955, "dur":250, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.StreamingModule.dll" }}
,{ "pid":12345, "tid":9, "ts":1748104828390953, "dur":255, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.StreamingModule.dll_BAC60BAFBF9E3FE8.mvfrm" }}
,{ "pid":12345, "tid":9, "ts":1748104828391267, "dur":52, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsSamplesModule.dll_07DB3F7C59D1852F.mvfrm" }}
,{ "pid":12345, "tid":9, "ts":1748104828391373, "dur":52, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AssetBundleModule.dll_C641301EBFF0B918.mvfrm" }}
,{ "pid":12345, "tid":9, "ts":1748104828391426, "dur":53, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1748104828391488, "dur":52, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DirectorModule.dll_26069FABF347DCAA.mvfrm" }}
,{ "pid":12345, "tid":9, "ts":1748104828391596, "dur":54, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ImageConversionModule.dll" }}
,{ "pid":12345, "tid":9, "ts":1748104828391594, "dur":59, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ImageConversionModule.dll_B9CC95F9FD7682B7.mvfrm" }}
,{ "pid":12345, "tid":9, "ts":1748104828391654, "dur":54, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1748104828391716, "dur":52, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VehiclesModule.dll_9DC9992B6D71AC45.mvfrm" }}
,{ "pid":12345, "tid":9, "ts":1748104828391769, "dur":169, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1748104828391949, "dur":63, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\PlaybackEngines\\WindowsStandaloneSupport\\UnityEditor.WindowsStandalone.Extensions.dll" }}
,{ "pid":12345, "tid":9, "ts":1748104828391946, "dur":70, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WindowsStandalone.Extensions.dll_6F29ACDB6AE0CD9E.mvfrm" }}
,{ "pid":12345, "tid":9, "ts":1748104828392082, "dur":246, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll.mvfrm.rsp" }}
,{ "pid":12345, "tid":9, "ts":1748104828392329, "dur":83, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1748104828392424, "dur":145, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll.mvfrm" }}
,{ "pid":12345, "tid":9, "ts":1748104828392570, "dur":106, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1748104828392892, "dur":92, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\mscorlib.dll" }}
,{ "pid":12345, "tid":9, "ts":1748104828392986, "dur":94, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.ComponentModel.Composition.dll" }}
,{ "pid":12345, "tid":9, "ts":1748104828393082, "dur":70, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Core.dll" }}
,{ "pid":12345, "tid":9, "ts":1748104828393202, "dur":51, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.dll" }}
,{ "pid":12345, "tid":9, "ts":1748104828393255, "dur":79, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Drawing.dll" }}
,{ "pid":12345, "tid":9, "ts":1748104828393336, "dur":76, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.IO.Compression.FileSystem.dll" }}
,{ "pid":12345, "tid":9, "ts":1748104828393414, "dur":100, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Net.dll" }}
,{ "pid":12345, "tid":9, "ts":1748104828393516, "dur":87, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Numerics.dll" }}
,{ "pid":12345, "tid":9, "ts":1748104828393605, "dur":105, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Runtime.Serialization.dll" }}
,{ "pid":12345, "tid":9, "ts":1748104828393712, "dur":70, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.ServiceModel.Web.dll" }}
,{ "pid":12345, "tid":9, "ts":1748104828393784, "dur":81, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Transactions.dll" }}
,{ "pid":12345, "tid":9, "ts":1748104828393867, "dur":65, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Web.dll" }}
,{ "pid":12345, "tid":9, "ts":1748104828393934, "dur":78, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Windows.dll" }}
,{ "pid":12345, "tid":9, "ts":1748104828394014, "dur":67, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Xml.dll" }}
,{ "pid":12345, "tid":9, "ts":1748104828394082, "dur":55, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Xml.Linq.dll" }}
,{ "pid":12345, "tid":9, "ts":1748104828394141, "dur":91, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Xml.Serialization.dll" }}
,{ "pid":12345, "tid":9, "ts":1748104828394235, "dur":105, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\Microsoft.Win32.Primitives.dll" }}
,{ "pid":12345, "tid":9, "ts":1748104828394342, "dur":74, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.AppContext.dll" }}
,{ "pid":12345, "tid":9, "ts":1748104828394418, "dur":74, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Buffers.dll" }}
,{ "pid":12345, "tid":9, "ts":1748104828394494, "dur":82, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Collections.Concurrent.dll" }}
,{ "pid":12345, "tid":9, "ts":1748104828394579, "dur":81, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Collections.dll" }}
,{ "pid":12345, "tid":9, "ts":1748104828394662, "dur":168, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Collections.NonGeneric.dll" }}
,{ "pid":12345, "tid":9, "ts":1748104828394883, "dur":60, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.ComponentModel.dll" }}
,{ "pid":12345, "tid":9, "ts":1748104828394946, "dur":71, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.ComponentModel.EventBasedAsync.dll" }}
,{ "pid":12345, "tid":9, "ts":1748104828395019, "dur":93, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.ComponentModel.Primitives.dll" }}
,{ "pid":12345, "tid":9, "ts":1748104828395113, "dur":76, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.ComponentModel.TypeConverter.dll" }}
,{ "pid":12345, "tid":9, "ts":1748104828395191, "dur":88, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Console.dll" }}
,{ "pid":12345, "tid":9, "ts":1748104828395281, "dur":130, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Data.Common.dll" }}
,{ "pid":12345, "tid":9, "ts":1748104828395412, "dur":60, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.Contracts.dll" }}
,{ "pid":12345, "tid":9, "ts":1748104828395473, "dur":83, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.Debug.dll" }}
,{ "pid":12345, "tid":9, "ts":1748104828395558, "dur":141, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.FileVersionInfo.dll" }}
,{ "pid":12345, "tid":9, "ts":1748104828395702, "dur":75, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.Process.dll" }}
,{ "pid":12345, "tid":9, "ts":1748104828395779, "dur":93, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.StackTrace.dll" }}
,{ "pid":12345, "tid":9, "ts":1748104828395875, "dur":107, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.TextWriterTraceListener.dll" }}
,{ "pid":12345, "tid":9, "ts":1748104828395985, "dur":93, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.Tools.dll" }}
,{ "pid":12345, "tid":9, "ts":1748104828396580, "dur":276, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.FileSystem.DriveInfo.dll" }}
,{ "pid":12345, "tid":9, "ts":1748104828396858, "dur":67, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.FileSystem.Primitives.dll" }}
,{ "pid":12345, "tid":9, "ts":1748104828396928, "dur":95, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.FileSystem.Watcher.dll" }}
,{ "pid":12345, "tid":9, "ts":1748104828397025, "dur":54, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.IsolatedStorage.dll" }}
,{ "pid":12345, "tid":9, "ts":1748104828397082, "dur":182, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.MemoryMappedFiles.dll" }}
,{ "pid":12345, "tid":9, "ts":1748104828397266, "dur":65, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.Pipes.dll" }}
,{ "pid":12345, "tid":9, "ts":1748104828397333, "dur":286, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.UnmanagedMemoryStream.dll" }}
,{ "pid":12345, "tid":9, "ts":1748104828397621, "dur":229, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Linq.dll" }}
,{ "pid":12345, "tid":9, "ts":1748104828397853, "dur":705, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Linq.Expressions.dll" }}
,{ "pid":12345, "tid":9, "ts":1748104828398566, "dur":71, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Linq.Parallel.dll" }}
,{ "pid":12345, "tid":9, "ts":1748104828398639, "dur":66, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Linq.Queryable.dll" }}
,{ "pid":12345, "tid":9, "ts":1748104828398707, "dur":71, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Memory.dll" }}
,{ "pid":12345, "tid":9, "ts":1748104828398780, "dur":66, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.Http.dll" }}
,{ "pid":12345, "tid":9, "ts":1748104828398849, "dur":71, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.NameResolution.dll" }}
,{ "pid":12345, "tid":9, "ts":1748104828399318, "dur":87, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Numerics.Vectors.dll" }}
,{ "pid":12345, "tid":9, "ts":1748104828399455, "dur":68, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Reflection.DispatchProxy.dll" }}
,{ "pid":12345, "tid":9, "ts":1748104828399526, "dur":77, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Reflection.dll" }}
,{ "pid":12345, "tid":9, "ts":1748104828399605, "dur":51, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Reflection.Emit.dll" }}
,{ "pid":12345, "tid":9, "ts":1748104828399752, "dur":88, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Reflection.Extensions.dll" }}
,{ "pid":12345, "tid":9, "ts":1748104828399843, "dur":72, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Reflection.Primitives.dll" }}
,{ "pid":12345, "tid":9, "ts":1748104828399916, "dur":52, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Resources.Reader.dll" }}
,{ "pid":12345, "tid":9, "ts":1748104828399971, "dur":56, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Resources.ResourceManager.dll" }}
,{ "pid":12345, "tid":9, "ts":1748104828400032, "dur":58, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.CompilerServices.VisualC.dll" }}
,{ "pid":12345, "tid":9, "ts":1748104828400092, "dur":61, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.dll" }}
,{ "pid":12345, "tid":9, "ts":1748104828400155, "dur":147, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.Extensions.dll" }}
,{ "pid":12345, "tid":9, "ts":1748104828400304, "dur":80, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.Handles.dll" }}
,{ "pid":12345, "tid":9, "ts":1748104828400386, "dur":54, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.InteropServices.dll" }}
,{ "pid":12345, "tid":9, "ts":1748104828400442, "dur":51, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.InteropServices.RuntimeInformation.dll" }}
,{ "pid":12345, "tid":9, "ts":1748104828400626, "dur":70, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.Serialization.Primitives.dll" }}
,{ "pid":12345, "tid":9, "ts":1748104828400699, "dur":86, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.Serialization.Xml.dll" }}
,{ "pid":12345, "tid":9, "ts":1748104828400788, "dur":61, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Security.Claims.dll" }}
,{ "pid":12345, "tid":9, "ts":1748104828400851, "dur":108, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Security.Cryptography.Algorithms.dll" }}
,{ "pid":12345, "tid":9, "ts":1748104828400962, "dur":61, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Security.Cryptography.Csp.dll" }}
,{ "pid":12345, "tid":9, "ts":1748104828401026, "dur":53, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Security.Cryptography.Encoding.dll" }}
,{ "pid":12345, "tid":9, "ts":1748104828401082, "dur":53, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Security.Cryptography.Primitives.dll" }}
,{ "pid":12345, "tid":9, "ts":1748104828401138, "dur":151, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Security.Cryptography.X509Certificates.dll" }}
,{ "pid":12345, "tid":9, "ts":1748104828401291, "dur":79, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Security.Principal.dll" }}
,{ "pid":12345, "tid":9, "ts":1748104828401376, "dur":70, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Security.SecureString.dll" }}
,{ "pid":12345, "tid":9, "ts":1748104828401497, "dur":64, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Text.Encoding.Extensions.dll" }}
,{ "pid":12345, "tid":9, "ts":1748104828401613, "dur":67, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Threading.dll" }}
,{ "pid":12345, "tid":9, "ts":1748104828401682, "dur":77, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Threading.Overlapped.dll" }}
,{ "pid":12345, "tid":9, "ts":1748104828401762, "dur":69, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Threading.Tasks.dll" }}
,{ "pid":12345, "tid":9, "ts":1748104828401835, "dur":91, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Threading.Tasks.Extensions.dll" }}
,{ "pid":12345, "tid":9, "ts":1748104828401928, "dur":55, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Threading.Tasks.Parallel.dll" }}
,{ "pid":12345, "tid":9, "ts":1748104828402256, "dur":83, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Xml.XmlDocument.dll" }}
,{ "pid":12345, "tid":9, "ts":1748104828402341, "dur":59, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Xml.XmlSerializer.dll" }}
,{ "pid":12345, "tid":9, "ts":1748104828402402, "dur":73, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Xml.XPath.dll" }}
,{ "pid":12345, "tid":9, "ts":1748104828402477, "dur":74, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Xml.XPath.XDocument.dll" }}
,{ "pid":12345, "tid":9, "ts":1748104828402553, "dur":58, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\NetStandard\\Extensions\\2.0.0\\System.Runtime.InteropServices.WindowsRuntime.dll" }}
,{ "pid":12345, "tid":9, "ts":1748104828402613, "dur":86, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\NetStandard\\ref\\2.1.0\\netstandard.dll" }}
,{ "pid":12345, "tid":9, "ts":1748104828402715, "dur":68, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\PackageCache\\com.unity.ext.nunit@1.0.6\\net35\\unity-custom\\nunit.framework.dll" }}
,{ "pid":12345, "tid":9, "ts":1748104828402785, "dur":58, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\AssemblyInfo.cs" }}
,{ "pid":12345, "tid":9, "ts":1748104828402845, "dur":59, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\Assertions\\AllocatingGCMemoryConstraint.cs" }}
,{ "pid":12345, "tid":9, "ts":1748104828402997, "dur":50, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\Assertions\\Is.cs" }}
,{ "pid":12345, "tid":9, "ts":1748104828403439, "dur":51, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\ActionDelegator.cs" }}
,{ "pid":12345, "tid":9, "ts":1748104828403492, "dur":52, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Attributes\\ConditionalIgnoreAttribute.cs" }}
,{ "pid":12345, "tid":9, "ts":1748104828403546, "dur":52, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Attributes\\TestEnumerator.cs" }}
,{ "pid":12345, "tid":9, "ts":1748104828403600, "dur":55, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Attributes\\TestMustExpectAllLogsAttribute.cs" }}
,{ "pid":12345, "tid":9, "ts":1748104828403713, "dur":54, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Attributes\\UnityPlatformAttribute.cs" }}
,{ "pid":12345, "tid":9, "ts":1748104828403769, "dur":56, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Attributes\\UnitySetUpAttribute.cs" }}
,{ "pid":12345, "tid":9, "ts":1748104828403827, "dur":53, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Attributes\\UnityTearDownAttribute.cs" }}
,{ "pid":12345, "tid":9, "ts":1748104828404077, "dur":50, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Commands\\EnumerableApplyChangesToContextCommand.cs" }}
,{ "pid":12345, "tid":9, "ts":1748104828404129, "dur":50, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Commands\\EnumerableRepeatedTestCommand.cs" }}
,{ "pid":12345, "tid":9, "ts":1748104828405485, "dur":763, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Runner\\UnityWorkItem.cs" }}
,{ "pid":12345, "tid":9, "ts":1748104828407283, "dur":52, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\TestRunner\\TestListenerWrapper.cs" }}
,{ "pid":12345, "tid":9, "ts":1748104828407489, "dur":53, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\Utils\\AssemblyProvider\\IAssemblyLoadProxy.cs" }}
,{ "pid":12345, "tid":9, "ts":1748104828407544, "dur":57, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\Utils\\AssemblyProvider\\IAssemblyWrapper.cs" }}
,{ "pid":12345, "tid":9, "ts":1748104828407654, "dur":63, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\Utils\\AssemblyProvider\\PlayerTestAssemblyProvider.cs" }}
,{ "pid":12345, "tid":9, "ts":1748104828407720, "dur":51, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\Utils\\AssemblyProvider\\ScriptingRuntimeProxy.cs" }}
,{ "pid":12345, "tid":9, "ts":1748104828407861, "dur":64, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\Utils\\CoroutineRunner.cs" }}
,{ "pid":12345, "tid":9, "ts":1748104828408020, "dur":57, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\Utils\\IPostBuildCleanup.cs" }}
,{ "pid":12345, "tid":9, "ts":1748104828408159, "dur":55, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\Utils\\MonoBehaviourTest\\IMonoBehaviourTest.cs" }}
,{ "pid":12345, "tid":9, "ts":1748104828408394, "dur":791, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\Utils\\StacktraceFilter.cs" }}
,{ "pid":12345, "tid":9, "ts":1748104828409583, "dur":388, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\Tools\\Unity.SourceGenerators\\Unity.Properties.SourceGenerator.dll" }}
,{ "pid":12345, "tid":9, "ts":1748104828409973, "dur":70, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\Tools\\Unity.SourceGenerators\\Unity.SourceGenerators.dll" }}
,{ "pid":12345, "tid":9, "ts":1748104828392688, "dur":17376, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll (+2 others)" }}
,{ "pid":12345, "tid":9, "ts":1748104828410067, "dur":135, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1748104828410243, "dur":186, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1748104828410455, "dur":580, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll.mvfrm" }}
,{ "pid":12345, "tid":9, "ts":1748104828411322, "dur":102, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\Microsoft.Win32.Primitives.dll" }}
,{ "pid":12345, "tid":9, "ts":1748104828411426, "dur":78, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\netstandard.dll" }}
,{ "pid":12345, "tid":9, "ts":1748104828411506, "dur":75, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.AppContext.dll" }}
,{ "pid":12345, "tid":9, "ts":1748104828411583, "dur":75, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Buffers.dll" }}
,{ "pid":12345, "tid":9, "ts":1748104828411660, "dur":84, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Collections.Concurrent.dll" }}
,{ "pid":12345, "tid":9, "ts":1748104828411747, "dur":82, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Collections.dll" }}
,{ "pid":12345, "tid":9, "ts":1748104828411831, "dur":74, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Collections.NonGeneric.dll" }}
,{ "pid":12345, "tid":9, "ts":1748104828411907, "dur":86, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Collections.Specialized.dll" }}
,{ "pid":12345, "tid":9, "ts":1748104828411996, "dur":83, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.ComponentModel.Annotations.dll" }}
,{ "pid":12345, "tid":9, "ts":1748104828412081, "dur":71, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.ComponentModel.dll" }}
,{ "pid":12345, "tid":9, "ts":1748104828412155, "dur":88, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.ComponentModel.EventBasedAsync.dll" }}
,{ "pid":12345, "tid":9, "ts":1748104828412245, "dur":87, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.ComponentModel.Primitives.dll" }}
,{ "pid":12345, "tid":9, "ts":1748104828412335, "dur":960, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.ComponentModel.TypeConverter.dll" }}
,{ "pid":12345, "tid":9, "ts":1748104828413297, "dur":93, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Console.dll" }}
,{ "pid":12345, "tid":9, "ts":1748104828413392, "dur":82, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Data.Common.dll" }}
,{ "pid":12345, "tid":9, "ts":1748104828413476, "dur":117, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Diagnostics.Contracts.dll" }}
,{ "pid":12345, "tid":9, "ts":1748104828413596, "dur":85, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Diagnostics.Debug.dll" }}
,{ "pid":12345, "tid":9, "ts":1748104828413688, "dur":98, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Diagnostics.FileVersionInfo.dll" }}
,{ "pid":12345, "tid":9, "ts":1748104828413789, "dur":75, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Diagnostics.Process.dll" }}
,{ "pid":12345, "tid":9, "ts":1748104828413867, "dur":78, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Diagnostics.StackTrace.dll" }}
,{ "pid":12345, "tid":9, "ts":1748104828413948, "dur":112, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Diagnostics.TextWriterTraceListener.dll" }}
,{ "pid":12345, "tid":9, "ts":1748104828414062, "dur":323, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Diagnostics.Tools.dll" }}
,{ "pid":12345, "tid":9, "ts":1748104828414388, "dur":411, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Diagnostics.TraceSource.dll" }}
,{ "pid":12345, "tid":9, "ts":1748104828414802, "dur":81, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Drawing.Primitives.dll" }}
,{ "pid":12345, "tid":9, "ts":1748104828414886, "dur":77, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Dynamic.Runtime.dll" }}
,{ "pid":12345, "tid":9, "ts":1748104828414966, "dur":92, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Globalization.Calendars.dll" }}
,{ "pid":12345, "tid":9, "ts":1748104828415060, "dur":76, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Globalization.dll" }}
,{ "pid":12345, "tid":9, "ts":1748104828415138, "dur":91, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Globalization.Extensions.dll" }}
,{ "pid":12345, "tid":9, "ts":1748104828415231, "dur":71, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.IO.Compression.ZipFile.dll" }}
,{ "pid":12345, "tid":9, "ts":1748104828415304, "dur":91, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.IO.dll" }}
,{ "pid":12345, "tid":9, "ts":1748104828415397, "dur":86, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.IO.FileSystem.dll" }}
,{ "pid":12345, "tid":9, "ts":1748104828415485, "dur":109, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.IO.FileSystem.DriveInfo.dll" }}
,{ "pid":12345, "tid":9, "ts":1748104828415597, "dur":104, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.IO.FileSystem.Primitives.dll" }}
,{ "pid":12345, "tid":9, "ts":1748104828415703, "dur":86, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.IO.FileSystem.Watcher.dll" }}
,{ "pid":12345, "tid":9, "ts":1748104828415792, "dur":87, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.IO.IsolatedStorage.dll" }}
,{ "pid":12345, "tid":9, "ts":1748104828415882, "dur":91, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.IO.MemoryMappedFiles.dll" }}
,{ "pid":12345, "tid":9, "ts":1748104828415976, "dur":89, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.IO.Pipes.dll" }}
,{ "pid":12345, "tid":9, "ts":1748104828416067, "dur":146, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.IO.UnmanagedMemoryStream.dll" }}
,{ "pid":12345, "tid":9, "ts":1748104828416215, "dur":95, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Linq.dll" }}
,{ "pid":12345, "tid":9, "ts":1748104828416312, "dur":91, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Linq.Expressions.dll" }}
,{ "pid":12345, "tid":9, "ts":1748104828416406, "dur":85, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Linq.Parallel.dll" }}
,{ "pid":12345, "tid":9, "ts":1748104828416494, "dur":82, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Linq.Queryable.dll" }}
,{ "pid":12345, "tid":9, "ts":1748104828416578, "dur":83, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Memory.dll" }}
,{ "pid":12345, "tid":9, "ts":1748104828416664, "dur":85, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Net.Http.Rtc.dll" }}
,{ "pid":12345, "tid":9, "ts":1748104828416752, "dur":86, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Net.NameResolution.dll" }}
,{ "pid":12345, "tid":9, "ts":1748104828416844, "dur":177, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Net.NetworkInformation.dll" }}
,{ "pid":12345, "tid":9, "ts":1748104828417024, "dur":112, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Net.Ping.dll" }}
,{ "pid":12345, "tid":9, "ts":1748104828417139, "dur":93, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Net.Primitives.dll" }}
,{ "pid":12345, "tid":9, "ts":1748104828417234, "dur":75, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Net.Requests.dll" }}
,{ "pid":12345, "tid":9, "ts":1748104828417311, "dur":77, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Net.Security.dll" }}
,{ "pid":12345, "tid":9, "ts":1748104828417390, "dur":84, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Net.Sockets.dll" }}
,{ "pid":12345, "tid":9, "ts":1748104828417477, "dur":97, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Net.WebHeaderCollection.dll" }}
,{ "pid":12345, "tid":9, "ts":1748104828417577, "dur":80, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Net.WebSockets.Client.dll" }}
,{ "pid":12345, "tid":9, "ts":1748104828417660, "dur":81, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Net.WebSockets.dll" }}
,{ "pid":12345, "tid":9, "ts":1748104828417743, "dur":76, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.ObjectModel.dll" }}
,{ "pid":12345, "tid":9, "ts":1748104828417821, "dur":91, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Reflection.DispatchProxy.dll" }}
,{ "pid":12345, "tid":9, "ts":1748104828417914, "dur":88, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Reflection.dll" }}
,{ "pid":12345, "tid":9, "ts":1748104828418004, "dur":75, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Reflection.Emit.dll" }}
,{ "pid":12345, "tid":9, "ts":1748104828418082, "dur":88, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Reflection.Emit.ILGeneration.dll" }}
,{ "pid":12345, "tid":9, "ts":1748104828418173, "dur":619, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Reflection.Emit.Lightweight.dll" }}
,{ "pid":12345, "tid":9, "ts":1748104828418795, "dur":81, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Reflection.Extensions.dll" }}
,{ "pid":12345, "tid":9, "ts":1748104828418878, "dur":98, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Reflection.Primitives.dll" }}
,{ "pid":12345, "tid":9, "ts":1748104828418979, "dur":150, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Resources.Reader.dll" }}
,{ "pid":12345, "tid":9, "ts":1748104828419131, "dur":91, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Resources.ResourceManager.dll" }}
,{ "pid":12345, "tid":9, "ts":1748104828419224, "dur":76, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Resources.Writer.dll" }}
,{ "pid":12345, "tid":9, "ts":1748104828419303, "dur":144, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Runtime.CompilerServices.VisualC.dll" }}
,{ "pid":12345, "tid":9, "ts":1748104828419449, "dur":72, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Runtime.dll" }}
,{ "pid":12345, "tid":9, "ts":1748104828419524, "dur":162, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Runtime.Extensions.dll" }}
,{ "pid":12345, "tid":9, "ts":1748104828419694, "dur":83, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Runtime.Handles.dll" }}
,{ "pid":12345, "tid":9, "ts":1748104828419779, "dur":97, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Runtime.InteropServices.dll" }}
,{ "pid":12345, "tid":9, "ts":1748104828419878, "dur":119, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Runtime.InteropServices.RuntimeInformation.dll" }}
,{ "pid":12345, "tid":9, "ts":1748104828420000, "dur":84, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Runtime.InteropServices.WindowsRuntime.dll" }}
,{ "pid":12345, "tid":9, "ts":1748104828420090, "dur":79, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Runtime.Numerics.dll" }}
,{ "pid":12345, "tid":9, "ts":1748104828420171, "dur":93, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Runtime.Serialization.Formatters.dll" }}
,{ "pid":12345, "tid":9, "ts":1748104828420265, "dur":71, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Runtime.Serialization.Json.dll" }}
,{ "pid":12345, "tid":9, "ts":1748104828420338, "dur":59, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Runtime.Serialization.Primitives.dll" }}
,{ "pid":12345, "tid":9, "ts":1748104828420399, "dur":57, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Runtime.Serialization.Xml.dll" }}
,{ "pid":12345, "tid":9, "ts":1748104828420457, "dur":69, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Security.Claims.dll" }}
,{ "pid":12345, "tid":9, "ts":1748104828420527, "dur":61, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Security.Cryptography.Algorithms.dll" }}
,{ "pid":12345, "tid":9, "ts":1748104828420589, "dur":59, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Security.Cryptography.Csp.dll" }}
,{ "pid":12345, "tid":9, "ts":1748104828420650, "dur":59, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Security.Cryptography.Encoding.dll" }}
,{ "pid":12345, "tid":9, "ts":1748104828420710, "dur":132, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Security.Cryptography.Primitives.dll" }}
,{ "pid":12345, "tid":9, "ts":1748104828420897, "dur":121, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Microsoft.CSharp.dll" }}
,{ "pid":12345, "tid":9, "ts":1748104828411097, "dur":10229, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll (+2 others)" }}
,{ "pid":12345, "tid":9, "ts":1748104828421328, "dur":102, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1748104828421501, "dur":53, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1748104828421575, "dur":123, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll.mvfrm" }}
,{ "pid":12345, "tid":9, "ts":1748104828421699, "dur":52, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1748104828421764, "dur":435, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll (+2 others)" }}
,{ "pid":12345, "tid":9, "ts":1748104828422199, "dur":86, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1748104828422350, "dur":51, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1748104828422448, "dur":172, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Services.Core.dll.mvfrm" }}
,{ "pid":12345, "tid":9, "ts":1748104828422621, "dur":55, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1748104828422740, "dur":65, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.AnimationModule.dll" }}
,{ "pid":12345, "tid":9, "ts":1748104828422984, "dur":308, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityCurlModule.dll" }}
,{ "pid":12345, "tid":9, "ts":1748104828422689, "dur":1088, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Services.Core.dll (+2 others)" }}
,{ "pid":12345, "tid":9, "ts":1748104828423778, "dur":159, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1748104828423953, "dur":51, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1748104828424014, "dur":168, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Services.Core.Internal.dll.mvfrm" }}
,{ "pid":12345, "tid":9, "ts":1748104828424183, "dur":60, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1748104828424455, "dur":124, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UmbraModule.dll" }}
,{ "pid":12345, "tid":9, "ts":1748104828424802, "dur":136, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Globalization.dll" }}
,{ "pid":12345, "tid":9, "ts":1748104828424249, "dur":1215, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Services.Core.Internal.dll (+2 others)" }}
,{ "pid":12345, "tid":9, "ts":1748104828425465, "dur":260, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1748104828425794, "dur":207, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Services.Core.Environments.dll.mvfrm" }}
,{ "pid":12345, "tid":9, "ts":1748104828426002, "dur":61, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1748104828426070, "dur":645, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Services.Core.Environments.dll (+2 others)" }}
,{ "pid":12345, "tid":9, "ts":1748104828426716, "dur":290, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1748104828427020, "dur":64, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1748104828427098, "dur":157, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Services.Analytics.dll.mvfrm" }}
,{ "pid":12345, "tid":9, "ts":1748104828427256, "dur":75, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1748104828427659, "dur":68, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.Contracts.dll" }}
,{ "pid":12345, "tid":9, "ts":1748104828427845, "dur":150, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.MemoryMappedFiles.dll" }}
,{ "pid":12345, "tid":9, "ts":1748104828428241, "dur":242, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\PackageCache\\com.unity.services.analytics@5.0.0\\Runtime\\Events\\AdImpression\\AnalyticsServiceInstance.AdImpression.cs" }}
,{ "pid":12345, "tid":9, "ts":1748104828427340, "dur":1260, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Services.Analytics.dll (+2 others)" }}
,{ "pid":12345, "tid":9, "ts":1748104828428606, "dur":126, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1748104828428747, "dur":61, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1748104828428821, "dur":135, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1748104828429240, "dur":180, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityGltf.RenderPipelines.dll.mvfrm" }}
,{ "pid":12345, "tid":9, "ts":1748104828429422, "dur":60, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1748104828429601, "dur":497, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.Physics2DModule.dll" }}
,{ "pid":12345, "tid":9, "ts":1748104828430202, "dur":55, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.VFXModule.dll" }}
,{ "pid":12345, "tid":9, "ts":1748104828430285, "dur":294, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.VRModule.dll" }}
,{ "pid":12345, "tid":9, "ts":1748104828430717, "dur":55, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.Compression.ZipFile.dll" }}
,{ "pid":12345, "tid":9, "ts":1748104828429490, "dur":1553, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1900b0aE.dag/UnityGltf.RenderPipelines.dll (+2 others)" }}
,{ "pid":12345, "tid":9, "ts":1748104828431044, "dur":189, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1748104828431243, "dur":167, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1748104828431421, "dur":277, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1748104828431708, "dur":83, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1748104828431800, "dur":87, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1748104828431903, "dur":458, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1748104828432366, "dur":150, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1748104828432516, "dur":1276, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1748104828433793, "dur":843, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1748104828434641, "dur":123, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1748104828434811, "dur":177, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1748104828434994, "dur":138, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1748104828435145, "dur":96737, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1748104828358678, "dur":31569, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1748104828390275, "dur":79, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityAnalyticsModule.dll" }}
,{ "pid":12345, "tid":10, "ts":1748104828390357, "dur":384, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe" }}
,{ "pid":12345, "tid":10, "ts":1748104828390256, "dur":486, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsModule.dll_7FA638FB4E89A68F.mvfrm" }}
,{ "pid":12345, "tid":10, "ts":1748104828390743, "dur":137, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1748104828390894, "dur":210, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.TextCoreFontEngineModule.dll" }}
,{ "pid":12345, "tid":10, "ts":1748104828390892, "dur":216, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreFontEngineModule.dll_AAD28F33B811FA2B.mvfrm" }}
,{ "pid":12345, "tid":10, "ts":1748104828391109, "dur":72, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1748104828391190, "dur":51, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PropertiesModule.dll_E7346DF165278C21.mvfrm" }}
,{ "pid":12345, "tid":10, "ts":1748104828391292, "dur":62, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.AccessibilityModule.dll" }}
,{ "pid":12345, "tid":10, "ts":1748104828391290, "dur":68, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AccessibilityModule.dll_FE104A7B93220B04.mvfrm" }}
,{ "pid":12345, "tid":10, "ts":1748104828391412, "dur":58, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ClothModule.dll" }}
,{ "pid":12345, "tid":10, "ts":1748104828391410, "dur":63, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClothModule.dll_A9A54C4CF8838E00.mvfrm" }}
,{ "pid":12345, "tid":10, "ts":1748104828391526, "dur":60, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.GameCenterModule.dll" }}
,{ "pid":12345, "tid":10, "ts":1748104828391524, "dur":65, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GameCenterModule.dll_B9F92EB2F23C27E5.mvfrm" }}
,{ "pid":12345, "tid":10, "ts":1748104828391646, "dur":52, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.JSONSerializeModule.dll_5E27D3B7925D6B8E.mvfrm" }}
,{ "pid":12345, "tid":10, "ts":1748104828391698, "dur":51, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1748104828391760, "dur":187, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.WindModule.dll" }}
,{ "pid":12345, "tid":10, "ts":1748104828391758, "dur":194, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.WindModule.dll_D2A05FE9B1E621B6.mvfrm" }}
,{ "pid":12345, "tid":10, "ts":1748104828391953, "dur":59, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1748104828392012, "dur":103, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.WindModule.dll_D2A05FE9B1E621B6.mvfrm" }}
,{ "pid":12345, "tid":10, "ts":1748104828392125, "dur":147, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.rsp2" }}
,{ "pid":12345, "tid":10, "ts":1748104828392273, "dur":54, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1748104828392334, "dur":271, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Services.Core.rsp" }}
,{ "pid":12345, "tid":10, "ts":1748104828392606, "dur":71, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1748104828392689, "dur":108, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteText Library/Bee/artifacts/1900b0aE.dag/UnityGLTF.Helpers.rsp2" }}
,{ "pid":12345, "tid":10, "ts":1748104828392798, "dur":56, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1748104828392884, "dur":199, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Services.Core.Environments.rsp" }}
,{ "pid":12345, "tid":10, "ts":1748104828393084, "dur":61, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1748104828393145, "dur":116, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Services.Core.Environments.rsp" }}
,{ "pid":12345, "tid":10, "ts":1748104828393263, "dur":224, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Services.Core.Scheduler.rsp" }}
,{ "pid":12345, "tid":10, "ts":1748104828393488, "dur":70, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1748104828393658, "dur":67, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1748104828393734, "dur":52, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1748104828393835, "dur":61, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.UnityAdditionalFile.txt" }}
,{ "pid":12345, "tid":10, "ts":1748104828393900, "dur":68, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1748104828393976, "dur":318, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Services.Analytics.rsp" }}
,{ "pid":12345, "tid":10, "ts":1748104828394295, "dur":53, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1748104828394348, "dur":196, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Services.Analytics.rsp" }}
,{ "pid":12345, "tid":10, "ts":1748104828394547, "dur":58, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1748104828394724, "dur":149, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Sprite.Editor.rsp2" }}
,{ "pid":12345, "tid":10, "ts":1748104828394924, "dur":268, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Sprite.Editor.rsp2" }}
,{ "pid":12345, "tid":10, "ts":1748104828395194, "dur":601, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.rsp" }}
,{ "pid":12345, "tid":10, "ts":1748104828395796, "dur":115, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1748104828395912, "dur":120, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.rsp" }}
,{ "pid":12345, "tid":10, "ts":1748104828396034, "dur":374, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteText Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.rsp" }}
,{ "pid":12345, "tid":10, "ts":1748104828396409, "dur":69, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1748104828396479, "dur":279, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.rsp" }}
,{ "pid":12345, "tid":10, "ts":1748104828397595, "dur":812, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.ads@4.4.2\\Runtime\\Advertisement\\Utilities\\CoroutineExecutor.cs" }}
,{ "pid":12345, "tid":10, "ts":1748104828398595, "dur":1364, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.ads@4.4.2\\Runtime\\Advertisement\\Utilities\\AssemblyInfoUtilities.cs" }}
,{ "pid":12345, "tid":10, "ts":1748104828400185, "dur":766, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.ads@4.4.2\\Runtime\\Advertisement\\Platform\\Unsupported\\UnsupportedPlatform.cs" }}
,{ "pid":12345, "tid":10, "ts":1748104828396759, "dur":5435, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1748104828402447, "dur":1139, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.services.core@1.12.5\\Editor\\Core\\Shared\\Clients\\CloudEnvironmentConfigProvider.Gen.cs" }}
,{ "pid":12345, "tid":10, "ts":1748104828403586, "dur":1909, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.services.core@1.12.5\\Editor\\Core\\Shared\\Clients\\AssemblyInfo.Gen.cs" }}
,{ "pid":12345, "tid":10, "ts":1748104828405577, "dur":1608, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.services.core@1.12.5\\Editor\\Core\\Scheduler\\ActionSchedulerCleaner.cs" }}
,{ "pid":12345, "tid":10, "ts":1748104828407358, "dur":3068, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.services.core@1.12.5\\Editor\\Core\\ProjectStateManagement\\IServiceFlagRequest.cs" }}
,{ "pid":12345, "tid":10, "ts":1748104828402195, "dur":8742, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1748104828410937, "dur":999, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1748104828411937, "dur":1181, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1748104828415550, "dur":690, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.collab-proxy@2.5.1\\Editor\\PlasticSCM\\Views\\PendingChanges\\PendingChangesSelection.cs" }}
,{ "pid":12345, "tid":10, "ts":1748104828413119, "dur":4279, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1748104828417399, "dur":1028, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1748104828418428, "dur":567, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEditor.TestRunner\\CommandLineTest\\TestStarter.cs" }}
,{ "pid":12345, "tid":10, "ts":1748104828418428, "dur":1669, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1748104828420098, "dur":1420, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1748104828421519, "dur":857, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1748104828422376, "dur":109, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1748104828422495, "dur":448, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll.mvfrm" }}
,{ "pid":12345, "tid":10, "ts":1748104828422944, "dur":77, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1748104828423408, "dur":818, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.Debug.dll" }}
,{ "pid":12345, "tid":10, "ts":1748104828424349, "dur":137, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Reflection.Emit.ILGeneration.dll" }}
,{ "pid":12345, "tid":10, "ts":1748104828424624, "dur":405, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Xml.ReaderWriter.dll" }}
,{ "pid":12345, "tid":10, "ts":1748104828423027, "dur":2267, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll (+2 others)" }}
,{ "pid":12345, "tid":10, "ts":1748104828425296, "dur":482, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1748104828425793, "dur":64, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1748104828425864, "dur":319, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Services.Core.Scheduler.dll.mvfrm" }}
,{ "pid":12345, "tid":10, "ts":1748104828426184, "dur":128, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1748104828426934, "dur":399, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Text.RegularExpressions.dll" }}
,{ "pid":12345, "tid":10, "ts":1748104828427365, "dur":321, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Xml.XDocument.dll" }}
,{ "pid":12345, "tid":10, "ts":1748104828427839, "dur":52, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\Tools\\Unity.SourceGenerators\\Unity.Properties.SourceGenerator.dll" }}
,{ "pid":12345, "tid":10, "ts":1748104828426338, "dur":1560, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Services.Core.Scheduler.dll (+2 others)" }}
,{ "pid":12345, "tid":10, "ts":1748104828427899, "dur":194, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1748104828428094, "dur":387, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Services.Core.Scheduler.dll (+2 others)" }}
,{ "pid":12345, "tid":10, "ts":1748104828428493, "dur":89, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1748104828428605, "dur":357, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Autodesk.Fbx.Editor.dll.mvfrm" }}
,{ "pid":12345, "tid":10, "ts":1748104828429459, "dur":179, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Reflection.Emit.ILGeneration.dll" }}
,{ "pid":12345, "tid":10, "ts":1748104828429017, "dur":851, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1900b0aE.dag/Autodesk.Fbx.Editor.dll (+2 others)" }}
,{ "pid":12345, "tid":10, "ts":1748104828429869, "dur":153, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1748104828430041, "dur":431, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1748104828430517, "dur":363, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1748104828430917, "dur":333, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1748104828431287, "dur":103, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1748104828431398, "dur":226, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1748104828431634, "dur":92, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1748104828431741, "dur":133, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1748104828431883, "dur":378, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1748104828432271, "dur":295, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1748104828432572, "dur":1285, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1748104828433857, "dur":992, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1748104828434849, "dur":97007, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1748104828358736, "dur":31531, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1748104828390289, "dur":68, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityAnalyticsCommonModule.dll" }}
,{ "pid":12345, "tid":11, "ts":1748104828390360, "dur":390, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe" }}
,{ "pid":12345, "tid":11, "ts":1748104828390273, "dur":478, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsCommonModule.dll_4E3BC7BE14DC636A.mvfrm" }}
,{ "pid":12345, "tid":11, "ts":1748104828390752, "dur":256, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1748104828391019, "dur":67, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.SpriteMaskModule.dll" }}
,{ "pid":12345, "tid":11, "ts":1748104828391017, "dur":72, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteMaskModule.dll_A325A14313ABBD5F.mvfrm" }}
,{ "pid":12345, "tid":11, "ts":1748104828391138, "dur":205, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.EditorToolbarModule.dll" }}
,{ "pid":12345, "tid":11, "ts":1748104828391135, "dur":212, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EditorToolbarModule.dll_A7CB6E2693BF392F.mvfrm" }}
,{ "pid":12345, "tid":11, "ts":1748104828391347, "dur":159, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1748104828391514, "dur":80, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.dll_60CC90FA4D19C4B8.mvfrm" }}
,{ "pid":12345, "tid":11, "ts":1748104828391595, "dur":137, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1748104828391740, "dur":54, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VideoModule.dll_67C673BCE0F8CAC1.mvfrm" }}
,{ "pid":12345, "tid":11, "ts":1748104828391794, "dur":215, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1748104828392010, "dur":74, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VideoModule.dll_67C673BCE0F8CAC1.mvfrm" }}
,{ "pid":12345, "tid":11, "ts":1748104828392089, "dur":343, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.rsp" }}
,{ "pid":12345, "tid":11, "ts":1748104828392433, "dur":69, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1748104828392510, "dur":76, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1748104828392594, "dur":191, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteText Library/Bee/artifacts/1900b0aE.dag/GLTFSerialization.rsp2" }}
,{ "pid":12345, "tid":11, "ts":1748104828392786, "dur":54, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1748104828392847, "dur":229, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Services.Core.Configuration.dll.mvfrm.rsp" }}
,{ "pid":12345, "tid":11, "ts":1748104828393077, "dur":64, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1748104828393211, "dur":130, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.UnityAdditionalFile.txt" }}
,{ "pid":12345, "tid":11, "ts":1748104828393343, "dur":242, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.dll.mvfrm.rsp" }}
,{ "pid":12345, "tid":11, "ts":1748104828393742, "dur":179, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Services.Analytics.rsp2" }}
,{ "pid":12345, "tid":11, "ts":1748104828393922, "dur":66, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1748104828393997, "dur":275, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Formats.Fbx.Runtime.rsp" }}
,{ "pid":12345, "tid":11, "ts":1748104828394273, "dur":74, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1748104828394435, "dur":205, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.EditorCoroutines.Editor.rsp" }}
,{ "pid":12345, "tid":11, "ts":1748104828394643, "dur":50, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1748104828394742, "dur":379, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/1900b0aE.dag/UnityGLTF.Interactivity.VisualScriptingInstall.rsp2" }}
,{ "pid":12345, "tid":11, "ts":1748104828395131, "dur":62, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1748104828395210, "dur":50, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1748104828395261, "dur":52, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll.mvfrm.rsp" }}
,{ "pid":12345, "tid":11, "ts":1748104828395314, "dur":196, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Tilemap.Editor.rsp" }}
,{ "pid":12345, "tid":11, "ts":1748104828395511, "dur":62, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1748104828395592, "dur":83, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.rsp" }}
,{ "pid":12345, "tid":11, "ts":1748104828395676, "dur":51, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1748104828395733, "dur":144, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteText Library/Bee/artifacts/1900b0aE.dag/Analytics.rsp" }}
,{ "pid":12345, "tid":11, "ts":1748104828395878, "dur":63, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1748104828395949, "dur":51, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1748104828396006, "dur":54, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1748104828398047, "dur":862, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.ads@4.4.2\\Editor\\DevX\\EditorGameServices\\Analytics\\EditorGameServiceAnalyticsSender.cs" }}
,{ "pid":12345, "tid":11, "ts":1748104828396066, "dur":3642, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1748104828399709, "dur":2362, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1748104828402072, "dur":930, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.services.core@1.12.5\\Editor\\Core\\Shared\\Infrastructure\\Collections\\CollectionExtensions.Gen.cs" }}
,{ "pid":12345, "tid":11, "ts":1748104828403539, "dur":904, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.services.core@1.12.5\\Editor\\Core\\Shared\\DependencyInversion\\ServiceCollection.Gen.cs" }}
,{ "pid":12345, "tid":11, "ts":1748104828404522, "dur":588, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.services.core@1.12.5\\Editor\\Core\\Shared\\DependencyInversion\\IServiceFactory.Gen.cs" }}
,{ "pid":12345, "tid":11, "ts":1748104828402072, "dur":3241, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1748104828405313, "dur":1046, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1748104828406360, "dur":1109, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1748104828407469, "dur":834, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1748104828408387, "dur":760, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.services.core@1.12.5\\Runtime\\Environments\\EnvironmentsOptionsExtensions.cs" }}
,{ "pid":12345, "tid":11, "ts":1748104828408304, "dur":1603, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1748104828409907, "dur":1357, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1748104828411481, "dur":718, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.timeline@1.7.6\\Runtime\\Utilities\\TimelineCreateUtilities.cs" }}
,{ "pid":12345, "tid":11, "ts":1748104828411265, "dur":2059, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1748104828413455, "dur":594, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.collab-proxy@2.5.1\\Editor\\PlasticSCM\\Views\\IncomingChanges\\Gluon\\IncomingChangesTab.cs" }}
,{ "pid":12345, "tid":11, "ts":1748104828414828, "dur":651, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.collab-proxy@2.5.1\\Editor\\PlasticSCM\\Views\\IncomingChanges\\DrawIncomingChangesOverview.cs" }}
,{ "pid":12345, "tid":11, "ts":1748104828415600, "dur":583, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.collab-proxy@2.5.1\\Editor\\PlasticSCM\\Views\\IncomingChanges\\Developer\\IsResolved.cs" }}
,{ "pid":12345, "tid":11, "ts":1748104828416866, "dur":969, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.collab-proxy@2.5.1\\Editor\\PlasticSCM\\Views\\IncomingChanges\\Developer\\IncomingChangesTab.cs" }}
,{ "pid":12345, "tid":11, "ts":1748104828417949, "dur":1761, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.collab-proxy@2.5.1\\Editor\\PlasticSCM\\Views\\IncomingChanges\\Developer\\DirectoryConflicts\\DrawDirectoryResolutionPanel.cs" }}
,{ "pid":12345, "tid":11, "ts":1748104828413324, "dur":6618, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1748104828419943, "dur":1582, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1748104828421525, "dur":847, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1748104828422373, "dur":87, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1748104828422461, "dur":172, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.SpatialTracking.dll.mvfrm" }}
,{ "pid":12345, "tid":11, "ts":1748104828422633, "dur":57, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1748104828423407, "dur":491, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\PlaybackEngines\\AndroidPlayer\\Unity.Android.Types.dll" }}
,{ "pid":12345, "tid":11, "ts":1748104828423973, "dur":64, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\Tools\\Unity.SourceGenerators\\Unity.Properties.SourceGenerator.dll" }}
,{ "pid":12345, "tid":11, "ts":1748104828422695, "dur":1347, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.SpatialTracking.dll (+2 others)" }}
,{ "pid":12345, "tid":11, "ts":1748104828424043, "dur":176, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1748104828424237, "dur":53, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1748104828424297, "dur":229, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.XR.LegacyInputHelpers.dll.mvfrm" }}
,{ "pid":12345, "tid":11, "ts":1748104828424527, "dur":76, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1748104828424624, "dur":431, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.dll" }}
,{ "pid":12345, "tid":11, "ts":1748104828425570, "dur":263, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Security.SecureString.dll" }}
,{ "pid":12345, "tid":11, "ts":1748104828424611, "dur":1347, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.XR.LegacyInputHelpers.dll (+2 others)" }}
,{ "pid":12345, "tid":11, "ts":1748104828425960, "dur":221, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1748104828426196, "dur":524, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Services.Core.Device.dll.mvfrm" }}
,{ "pid":12345, "tid":11, "ts":1748104828426721, "dur":177, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1748104828426905, "dur":804, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Services.Core.Device.dll (+2 others)" }}
,{ "pid":12345, "tid":11, "ts":1748104828427710, "dur":243, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1748104828427999, "dur":59, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1748104828428058, "dur":414, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Services.Core.Device.ref.dll_EC89AF6B778C7413.mvfrm" }}
,{ "pid":12345, "tid":11, "ts":1748104828428476, "dur":365, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/McpUnity.Editor.dll.mvfrm" }}
,{ "pid":12345, "tid":11, "ts":1748104828428842, "dur":66, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1748104828428984, "dur":486, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1748104828429478, "dur":645, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll.mvfrm" }}
,{ "pid":12345, "tid":11, "ts":1748104828430124, "dur":114, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1748104828430276, "dur":122, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEditor.Graphs.dll" }}
,{ "pid":12345, "tid":11, "ts":1748104828430570, "dur":123, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.TerrainModule.dll" }}
,{ "pid":12345, "tid":11, "ts":1748104828430274, "dur":934, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll (+2 others)" }}
,{ "pid":12345, "tid":11, "ts":1748104828431208, "dur":147, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1748104828431366, "dur":252, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1748104828431630, "dur":93, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1748104828431761, "dur":149, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1748104828431921, "dur":115, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1748104828432045, "dur":90, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1748104828432144, "dur":258, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1748104828432429, "dur":72, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1748104828432512, "dur":101, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1748104828432622, "dur":1111, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1748104828433734, "dur":83, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1748104828433817, "dur":980, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1748104828434811, "dur":101, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1748104828434919, "dur":96934, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1748104828358802, "dur":31495, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1748104828390320, "dur":212, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UmbraModule.dll" }}
,{ "pid":12345, "tid":12, "ts":1748104828390533, "dur":179, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe" }}
,{ "pid":12345, "tid":12, "ts":1748104828390305, "dur":409, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UmbraModule.dll_BD919B6AAB8D5A8F.mvfrm" }}
,{ "pid":12345, "tid":12, "ts":1748104828390725, "dur":165, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1748104828390949, "dur":53, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1748104828391013, "dur":351, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.SpriteShapeModule.dll" }}
,{ "pid":12345, "tid":12, "ts":1748104828391011, "dur":356, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteShapeModule.dll_DFA25D797592F122.mvfrm" }}
,{ "pid":12345, "tid":12, "ts":1748104828391367, "dur":640, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1748104828392067, "dur":53, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\PackageCache\\com.unity.analytics@3.8.1\\AnalyticsStandardEvents\\Unity.Analytics.StandardEvents.dll" }}
,{ "pid":12345, "tid":12, "ts":1748104828392014, "dur":109, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Analytics.StandardEvents.dll_2539AF974BFE7ABE.mvfrm" }}
,{ "pid":12345, "tid":12, "ts":1748104828392185, "dur":208, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll.mvfrm.rsp" }}
,{ "pid":12345, "tid":12, "ts":1748104828392394, "dur":100, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1748104828392500, "dur":395, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.rsp" }}
,{ "pid":12345, "tid":12, "ts":1748104828392896, "dur":69, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1748104828393033, "dur":63, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1748104828393104, "dur":51, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1748104828393191, "dur":131, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1748104828393346, "dur":401, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.rsp" }}
,{ "pid":12345, "tid":12, "ts":1748104828393749, "dur":62, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1748104828393818, "dur":99, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.rsp2" }}
,{ "pid":12345, "tid":12, "ts":1748104828393918, "dur":69, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1748104828393994, "dur":283, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Services.Core.Editor.rsp" }}
,{ "pid":12345, "tid":12, "ts":1748104828394278, "dur":57, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1748104828394344, "dur":199, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteText Library/Bee/artifacts/1900b0aE.dag/Autodesk.Fbx.rsp" }}
,{ "pid":12345, "tid":12, "ts":1748104828394545, "dur":52, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1748104828394605, "dur":50, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1748104828394937, "dur":341, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/1900b0aE.dag/UnityGLTF.Interactivity.Runtime.dll.mvfrm.rsp" }}
,{ "pid":12345, "tid":12, "ts":1748104828395280, "dur":250, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.SpatialTracking.rsp" }}
,{ "pid":12345, "tid":12, "ts":1748104828395532, "dur":116, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1748104828395689, "dur":211, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1748104828396038, "dur":474, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Services.Core.Environments.Editor.rsp" }}
,{ "pid":12345, "tid":12, "ts":1748104828396513, "dur":54, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1748104828396567, "dur":204, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Services.Core.Environments.Editor.rsp" }}
,{ "pid":12345, "tid":12, "ts":1748104828396772, "dur":662, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.ads@4.4.2\\Runtime\\Advertisement\\Platform\\iOS\\IosPlatform.cs" }}
,{ "pid":12345, "tid":12, "ts":1748104828397962, "dur":773, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.ads@4.4.2\\Runtime\\Advertisement\\Platform\\INativePlatform.cs" }}
,{ "pid":12345, "tid":12, "ts":1748104828398735, "dur":1087, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.ads@4.4.2\\Runtime\\Advertisement\\Platform\\Editor\\Placeholder.cs" }}
,{ "pid":12345, "tid":12, "ts":1748104828400162, "dur":682, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.ads@4.4.2\\Runtime\\Advertisement\\Platform\\Android\\BannerBundle.cs" }}
,{ "pid":12345, "tid":12, "ts":1748104828401083, "dur":510, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.ads@4.4.2\\Runtime\\Advertisement\\Platform\\Android\\AndroidPlatform.cs" }}
,{ "pid":12345, "tid":12, "ts":1748104828402224, "dur":1864, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.ads@4.4.2\\Runtime\\Advertisement\\Platform\\Android\\AndroidBanner.cs" }}
,{ "pid":12345, "tid":12, "ts":1748104828396772, "dur":7742, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1748104828404515, "dur":965, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1748104828405481, "dur":1142, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1748104828406624, "dur":951, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1748104828408387, "dur":758, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\org.khronos.unitygltf@1d3d93121c\\Runtime\\Plugins\\GLTFSerialization\\Schema\\TextureInfo.cs" }}
,{ "pid":12345, "tid":12, "ts":1748104828407576, "dur":1786, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1748104828409362, "dur":1022, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1748104828410389, "dur":512, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1748104828410914, "dur":979, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1748104828412332, "dur":942, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.textmeshpro@3.0.7\\Scripts\\Runtime\\TMP_FontAssetUtilities.cs" }}
,{ "pid":12345, "tid":12, "ts":1748104828413428, "dur":624, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.textmeshpro@3.0.7\\Scripts\\Runtime\\TMP_Dropdown.cs" }}
,{ "pid":12345, "tid":12, "ts":1748104828411894, "dur":2432, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1748104828414637, "dur":526, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.collab-proxy@2.5.1\\Editor\\PlasticSCM\\Inspector\\InspectorAssetSelection.cs" }}
,{ "pid":12345, "tid":12, "ts":1748104828415164, "dur":533, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.collab-proxy@2.5.1\\Editor\\PlasticSCM\\Inspector\\DrawInspectorOperations.cs" }}
,{ "pid":12345, "tid":12, "ts":1748104828414327, "dur":3346, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1748104828417674, "dur":1274, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1748104828418949, "dur":995, "ph":"X", "name": "File",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Runtime.Handles.dll" }}
,{ "pid":12345, "tid":12, "ts":1748104828418949, "dur":1026, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1748104828419975, "dur":1557, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1748104828421532, "dur":839, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1748104828422371, "dur":87, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1748104828422462, "dur":173, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll.mvfrm" }}
,{ "pid":12345, "tid":12, "ts":1748104828422636, "dur":122, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1748104828422984, "dur":51, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityWebRequestModule.dll" }}
,{ "pid":12345, "tid":12, "ts":1748104828423284, "dur":131, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.Security.dll" }}
,{ "pid":12345, "tid":12, "ts":1748104828423968, "dur":54, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\PackageCache\\com.unity.render-pipelines.core@14.0.11\\Runtime\\RenderGraph\\RenderGraphResourceRegistry.cs" }}
,{ "pid":12345, "tid":12, "ts":1748104828422768, "dur":1478, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll (+2 others)" }}
,{ "pid":12345, "tid":12, "ts":1748104828424247, "dur":229, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1748104828424491, "dur":159, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1748104828424658, "dur":300, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.dll.mvfrm" }}
,{ "pid":12345, "tid":12, "ts":1748104828424959, "dur":528, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1748104828425812, "dur":471, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.XRModule.dll" }}
,{ "pid":12345, "tid":12, "ts":1748104828426973, "dur":356, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\PackageCache\\com.unity.render-pipelines.core@14.0.11\\Editor\\Debugging\\DebugUIDrawer.cs" }}
,{ "pid":12345, "tid":12, "ts":1748104828427661, "dur":108, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\PackageCache\\com.unity.render-pipelines.core@14.0.11\\Editor\\RenderPipeline\\RenderPipelineGlobalSettingsUI.Drawers.cs" }}
,{ "pid":12345, "tid":12, "ts":1748104828427772, "dur":89, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\PackageCache\\com.unity.render-pipelines.core@14.0.11\\Editor\\RenderPipeline\\RenderPipelineGlobalSettingsUI.Skin.cs" }}
,{ "pid":12345, "tid":12, "ts":1748104828427961, "dur":67, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\PackageCache\\com.unity.render-pipelines.core@14.0.11\\Editor\\Volume\\VolumeProfileEditor.cs" }}
,{ "pid":12345, "tid":12, "ts":1748104828425498, "dur":2544, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.dll (+2 others)" }}
,{ "pid":12345, "tid":12, "ts":1748104828428043, "dur":301, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1748104828428359, "dur":50, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1748104828428624, "dur":180, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe" }}
,{ "pid":12345, "tid":12, "ts":1748104828428416, "dur":391, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.dll.mvfrm" }}
,{ "pid":12345, "tid":12, "ts":1748104828428807, "dur":70, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1748104828429180, "dur":227, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.IO.Compression.ZipFile.dll" }}
,{ "pid":12345, "tid":12, "ts":1748104828430808, "dur":85, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\PackageCache\\com.unity.shadergraph@14.0.11\\Editor\\Data\\Nodes\\Utility\\CustomFunctionNode.cs" }}
,{ "pid":12345, "tid":12, "ts":1748104828431582, "dur":279, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\PackageCache\\com.unity.shadergraph@14.0.11\\Editor\\Generation\\Enumerations\\ZWrite.cs" }}
,{ "pid":12345, "tid":12, "ts":1748104828432083, "dur":61, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\PackageCache\\com.unity.shadergraph@14.0.11\\Editor\\ShaderGraphAnalytics.cs" }}
,{ "pid":12345, "tid":12, "ts":1748104828428886, "dur":3350, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.dll (+2 others)" }}
,{ "pid":12345, "tid":12, "ts":1748104828432238, "dur":237, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1748104828432511, "dur":58, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1748104828432578, "dur":158, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityGLTF.ShaderGraph.dll.mvfrm" }}
,{ "pid":12345, "tid":12, "ts":1748104828432737, "dur":64, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1748104828432808, "dur":393, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1900b0aE.dag/UnityGLTF.ShaderGraph.dll (+2 others)" }}
,{ "pid":12345, "tid":12, "ts":1748104828433202, "dur":126, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1748104828433349, "dur":112, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1748104828433468, "dur":140, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1748104828433609, "dur":192, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1748104828433801, "dur":1051, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1748104828434853, "dur":97023, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1748104828358846, "dur":31473, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1748104828390336, "dur":217, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UIModule.dll" }}
,{ "pid":12345, "tid":13, "ts":1748104828390554, "dur":149, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe" }}
,{ "pid":12345, "tid":13, "ts":1748104828390326, "dur":378, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIModule.dll_395E6EB344FE5BE2.mvfrm" }}
,{ "pid":12345, "tid":13, "ts":1748104828390753, "dur":134, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1748104828390900, "dur":54, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.PhysicsModule.dll" }}
,{ "pid":12345, "tid":13, "ts":1748104828390985, "dur":246, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe" }}
,{ "pid":12345, "tid":13, "ts":1748104828390898, "dur":334, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PhysicsModule.dll_CA977A7B2B73956D.mvfrm" }}
,{ "pid":12345, "tid":13, "ts":1748104828391233, "dur":108, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1748104828391351, "dur":100, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.AnimationModule.dll" }}
,{ "pid":12345, "tid":13, "ts":1748104828391349, "dur":105, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AnimationModule.dll_06311E5F701688AA.mvfrm" }}
,{ "pid":12345, "tid":13, "ts":1748104828391455, "dur":142, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1748104828391604, "dur":52, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputLegacyModule.dll_57864B3D57E26F09.mvfrm" }}
,{ "pid":12345, "tid":13, "ts":1748104828391707, "dur":50, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.Physics2DModule.dll" }}
,{ "pid":12345, "tid":13, "ts":1748104828391705, "dur":57, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.Physics2DModule.dll_52D70E501113803F.mvfrm" }}
,{ "pid":12345, "tid":13, "ts":1748104828391763, "dur":160, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1748104828391935, "dur":88, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\PlaybackEngines\\AndroidPlayer\\Unity.Android.Types.dll" }}
,{ "pid":12345, "tid":13, "ts":1748104828391933, "dur":93, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Android.Types.dll_F0FC089AAFC61CD2.mvfrm" }}
,{ "pid":12345, "tid":13, "ts":1748104828392027, "dur":58, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1748104828392102, "dur":466, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.rsp" }}
,{ "pid":12345, "tid":13, "ts":1748104828392569, "dur":55, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1748104828392633, "dur":220, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.rsp" }}
,{ "pid":12345, "tid":13, "ts":1748104828392854, "dur":89, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1748104828392956, "dur":59, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1748104828393023, "dur":164, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteText Library/Bee/artifacts/1900b0aE.dag/UnityGLTFScripts.dll.mvfrm.rsp" }}
,{ "pid":12345, "tid":13, "ts":1748104828393189, "dur":83, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1748104828393292, "dur":230, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Services.Core.Device.rsp" }}
,{ "pid":12345, "tid":13, "ts":1748104828393524, "dur":51, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1748104828393581, "dur":217, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Services.Core.Configuration.Editor.rsp" }}
,{ "pid":12345, "tid":13, "ts":1748104828393800, "dur":77, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1748104828393910, "dur":53, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1748104828393979, "dur":52, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1748104828394038, "dur":52, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1748104828394272, "dur":133, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1748104828394406, "dur":241, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.UnityAdditionalFile.txt" }}
,{ "pid":12345, "tid":13, "ts":1748104828394652, "dur":59, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1748104828394721, "dur":51, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1748104828394779, "dur":77, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1748104828394874, "dur":53, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1748104828394927, "dur":323, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/1900b0aE.dag/UnityGLTF.Interactivity.VisualScriptingInstall.dll.mvfrm.rsp" }}
,{ "pid":12345, "tid":13, "ts":1748104828395253, "dur":139, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm.rsp" }}
,{ "pid":12345, "tid":13, "ts":1748104828395393, "dur":85, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1748104828395485, "dur":269, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.rsp" }}
,{ "pid":12345, "tid":13, "ts":1748104828395755, "dur":63, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1748104828395882, "dur":50, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1748104828395938, "dur":64, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1748104828396618, "dur":505, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.services.core@1.12.5\\Editor\\Core\\Environments\\ProjectInfo.cs" }}
,{ "pid":12345, "tid":13, "ts":1748104828396071, "dur":2061, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1748104828398881, "dur":525, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.ide.rider@3.0.31\\Rider\\Editor\\LoggingLevel.cs" }}
,{ "pid":12345, "tid":13, "ts":1748104828399852, "dur":1226, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.mathematics@1.2.6\\Unity.Mathematics.Editor\\PrimitiveVectorDrawer.cs" }}
,{ "pid":12345, "tid":13, "ts":1748104828398133, "dur":3824, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1748104828401958, "dur":1222, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1748104828403181, "dur":858, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1748104828404040, "dur":923, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1748104828405590, "dur":1588, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.shadergraph@14.0.11\\Editor\\Drawing\\Colors\\PrecisionColors.cs" }}
,{ "pid":12345, "tid":13, "ts":1748104828407343, "dur":1797, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.shadergraph@14.0.11\\Editor\\Drawing\\Colors\\CategoryColors.cs" }}
,{ "pid":12345, "tid":13, "ts":1748104828404963, "dur":4236, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1748104828409199, "dur":892, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1748104828410092, "dur":1165, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1748104828411258, "dur":1511, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1748104828412769, "dur":1240, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1748104828415024, "dur":572, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.collab-proxy@2.5.1\\Editor\\PlasticSCM\\SwitchModeConfirmationDialog.cs" }}
,{ "pid":12345, "tid":13, "ts":1748104828414010, "dur":3569, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1748104828417579, "dur":274, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1748104828417854, "dur":1089, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1748104828419039, "dur":1025, "ph":"X", "name": "File",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.ServiceModel.NetTcp.dll" }}
,{ "pid":12345, "tid":13, "ts":1748104828418944, "dur":1911, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1748104828420855, "dur":660, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1748104828421515, "dur":826, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1748104828422347, "dur":461, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1748104828422813, "dur":391, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll.mvfrm" }}
,{ "pid":12345, "tid":13, "ts":1748104828423204, "dur":61, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1748104828423283, "dur":131, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.DeviceSimulatorModule.dll" }}
,{ "pid":12345, "tid":13, "ts":1748104828424456, "dur":122, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\PackageCache\\com.unity.collab-proxy@2.5.1\\Editor\\PlasticSCM\\UI\\DrawActionHelpBox.cs" }}
,{ "pid":12345, "tid":13, "ts":1748104828424627, "dur":54, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\PackageCache\\com.unity.collab-proxy@2.5.1\\Editor\\PlasticSCM\\UI\\FindEditorWindow.cs" }}
,{ "pid":12345, "tid":13, "ts":1748104828423277, "dur":1988, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll (+2 others)" }}
,{ "pid":12345, "tid":13, "ts":1748104828425266, "dur":435, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1748104828425805, "dur":113, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ClothModule.dll" }}
,{ "pid":12345, "tid":13, "ts":1748104828426275, "dur":411, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.FileSystem.DriveInfo.dll" }}
,{ "pid":12345, "tid":13, "ts":1748104828425734, "dur":1272, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Services.Core.Analytics.dll (+2 others)" }}
,{ "pid":12345, "tid":13, "ts":1748104828427008, "dur":186, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1748104828427209, "dur":54, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1748104828427277, "dur":312, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.XR.LegacyInputHelpers.dll.mvfrm" }}
,{ "pid":12345, "tid":13, "ts":1748104828427590, "dur":86, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1748104828427768, "dur":82, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.InputModule.dll" }}
,{ "pid":12345, "tid":13, "ts":1748104828428242, "dur":355, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Security.SecureString.dll" }}
,{ "pid":12345, "tid":13, "ts":1748104828427681, "dur":1146, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.XR.LegacyInputHelpers.dll (+2 others)" }}
,{ "pid":12345, "tid":13, "ts":1748104828428828, "dur":197, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1748104828429038, "dur":128, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1748104828429179, "dur":68, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEditor.Graphs.dll" }}
,{ "pid":12345, "tid":13, "ts":1748104828429459, "dur":279, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityWebRequestWWWModule.dll" }}
,{ "pid":12345, "tid":13, "ts":1748104828429177, "dur":1166, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1900b0aE.dag/McpUnity.Editor.dll (+2 others)" }}
,{ "pid":12345, "tid":13, "ts":1748104828430344, "dur":147, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1748104828430506, "dur":377, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1748104828430884, "dur":108, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles Library/ScriptAssemblies/Unity.Services.Core.dll" }}
,{ "pid":12345, "tid":13, "ts":1748104828430999, "dur":136, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1748104828431135, "dur":83, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles Library/ScriptAssemblies/Unity.Formats.Fbx.Editor.dll" }}
,{ "pid":12345, "tid":13, "ts":1748104828431223, "dur":411, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1748104828431651, "dur":106, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1748104828431767, "dur":968, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1748104828432742, "dur":849, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1748104828433602, "dur":134, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1748104828433827, "dur":1009, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1748104828434836, "dur":97039, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":14, "ts":1748104828358882, "dur":31451, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":14, "ts":1748104828390355, "dur":56, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UIElementsModule.dll" }}
,{ "pid":12345, "tid":14, "ts":1748104828390413, "dur":365, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe" }}
,{ "pid":12345, "tid":14, "ts":1748104828390340, "dur":439, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIElementsModule.dll_1211690CE51FE169.mvfrm" }}
,{ "pid":12345, "tid":14, "ts":1748104828390780, "dur":230, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":14, "ts":1748104828391023, "dur":51, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.SharedInternalsModule.dll" }}
,{ "pid":12345, "tid":14, "ts":1748104828391020, "dur":59, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SharedInternalsModule.dll_3DDDD4CF74A47E07.mvfrm" }}
,{ "pid":12345, "tid":14, "ts":1748104828391133, "dur":51, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.dll_2E284A21EEA637D3.mvfrm" }}
,{ "pid":12345, "tid":14, "ts":1748104828391185, "dur":54, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":14, "ts":1748104828391247, "dur":54, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIBuilderModule.dll_8C344F5308722D38.mvfrm" }}
,{ "pid":12345, "tid":14, "ts":1748104828391356, "dur":51, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ARModule.dll_F75D958945D4B4FF.mvfrm" }}
,{ "pid":12345, "tid":14, "ts":1748104828391463, "dur":52, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.CoreModule.dll" }}
,{ "pid":12345, "tid":14, "ts":1748104828391461, "dur":57, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CoreModule.dll_5C5184C120AFA34D.mvfrm" }}
,{ "pid":12345, "tid":14, "ts":1748104828391519, "dur":115, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":14, "ts":1748104828391642, "dur":53, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputModule.dll_A932B702949ED3C0.mvfrm" }}
,{ "pid":12345, "tid":14, "ts":1748104828391794, "dur":230, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":14, "ts":1748104828392033, "dur":70, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\PackageCache\\com.unity.collab-proxy@2.5.1\\Lib\\Editor\\PlasticSCM\\Unity.Plastic.Antlr3.Runtime.dll" }}
,{ "pid":12345, "tid":14, "ts":1748104828392030, "dur":76, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Plastic.Antlr3.Runtime.dll_99AF08D3D11EB297.mvfrm" }}
,{ "pid":12345, "tid":14, "ts":1748104828392168, "dur":53, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":14, "ts":1748104828392232, "dur":321, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.rsp" }}
,{ "pid":12345, "tid":14, "ts":1748104828392554, "dur":67, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":14, "ts":1748104828392677, "dur":62, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":14, "ts":1748104828392856, "dur":50, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Services.Core.Configuration.rsp2" }}
,{ "pid":12345, "tid":14, "ts":1748104828392909, "dur":64, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":14, "ts":1748104828392981, "dur":52, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":14, "ts":1748104828393040, "dur":202, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteText Library/Bee/artifacts/1900b0aE.dag/UnityGLTFScripts.rsp" }}
,{ "pid":12345, "tid":14, "ts":1748104828393295, "dur":152, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Services.Core.Environments.Internal.rsp" }}
,{ "pid":12345, "tid":14, "ts":1748104828393448, "dur":60, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":14, "ts":1748104828393533, "dur":52, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":14, "ts":1748104828393653, "dur":85, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":14, "ts":1748104828393761, "dur":52, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":14, "ts":1748104828393880, "dur":534, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.rsp" }}
,{ "pid":12345, "tid":14, "ts":1748104828394464, "dur":325, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.rsp" }}
,{ "pid":12345, "tid":14, "ts":1748104828394791, "dur":61, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":14, "ts":1748104828394864, "dur":50, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":14, "ts":1748104828394914, "dur":239, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.Advertisements.Editor.dll.mvfrm.rsp" }}
,{ "pid":12345, "tid":14, "ts":1748104828395165, "dur":54, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":14, "ts":1748104828395283, "dur":304, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteText Library/Bee/artifacts/1900b0aE.dag/UnityGLTF.Interactivity.VisualScriptingInstall.rsp" }}
,{ "pid":12345, "tid":14, "ts":1748104828395588, "dur":59, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":14, "ts":1748104828395788, "dur":58, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":14, "ts":1748104828395900, "dur":55, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/1900b0aE.dag/FbxBuildTestAssets.rsp2" }}
,{ "pid":12345, "tid":14, "ts":1748104828396018, "dur":177, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteText Library/Bee/artifacts/1900b0aE.dag/UnityGltf.RenderPipelines.rsp" }}
,{ "pid":12345, "tid":14, "ts":1748104828396196, "dur":69, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":14, "ts":1748104828396271, "dur":1284, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":14, "ts":1748104828397556, "dur":1321, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.ide.visualstudio@2.0.22\\Editor\\KnownAssemblies.cs" }}
,{ "pid":12345, "tid":14, "ts":1748104828399317, "dur":843, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.ide.visualstudio@2.0.22\\Editor\\AsyncOperation.cs" }}
,{ "pid":12345, "tid":14, "ts":1748104828397556, "dur":4433, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":14, "ts":1748104828401989, "dur":1232, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":14, "ts":1748104828403221, "dur":903, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":14, "ts":1748104828404125, "dur":888, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":14, "ts":1748104828405014, "dur":1288, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":14, "ts":1748104828406302, "dur":835, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":14, "ts":1748104828407137, "dur":1164, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":14, "ts":1748104828408353, "dur":804, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\org.khronos.unitygltf@1d3d93121c\\Runtime\\Plugins\\GLTFSerialization\\Exceptions.cs" }}
,{ "pid":12345, "tid":14, "ts":1748104828409962, "dur":560, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.services.core@1.12.5\\Runtime\\Scheduler\\MinimumBinaryHeap.cs" }}
,{ "pid":12345, "tid":14, "ts":1748104828408301, "dur":2221, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":14, "ts":1748104828410528, "dur":88, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":14, "ts":1748104828410831, "dur":1530, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.timeline@1.7.6\\Editor\\Utilities\\ControlPlayableUtility.cs" }}
,{ "pid":12345, "tid":14, "ts":1748104828410623, "dur":2323, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":14, "ts":1748104828412947, "dur":900, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":14, "ts":1748104828416089, "dur":504, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.collab-proxy@2.5.1\\Editor\\PlasticSCM\\UI\\Avatar\\GetAvatar.cs" }}
,{ "pid":12345, "tid":14, "ts":1748104828413847, "dur":3055, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":14, "ts":1748104828416903, "dur":2630, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":14, "ts":1748104828419593, "dur":1934, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":14, "ts":1748104828421527, "dur":850, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":14, "ts":1748104828422377, "dur":86, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":14, "ts":1748104828422484, "dur":392, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.dll.mvfrm" }}
,{ "pid":12345, "tid":14, "ts":1748104828422877, "dur":76, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":14, "ts":1748104828423408, "dur":834, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.MemoryMappedFiles.dll" }}
,{ "pid":12345, "tid":14, "ts":1748104828424348, "dur":222, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.Numerics.dll" }}
,{ "pid":12345, "tid":14, "ts":1748104828422964, "dur":1821, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.dll (+2 others)" }}
,{ "pid":12345, "tid":14, "ts":1748104828424786, "dur":196, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":14, "ts":1748104828425002, "dur":59, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":14, "ts":1748104828425068, "dur":180, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.dll.mvfrm" }}
,{ "pid":12345, "tid":14, "ts":1748104828425464, "dur":51, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.TerrainModule.dll" }}
,{ "pid":12345, "tid":14, "ts":1748104828425924, "dur":315, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Xml.XmlSerializer.dll" }}
,{ "pid":12345, "tid":14, "ts":1748104828425302, "dur":1124, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.dll (+2 others)" }}
,{ "pid":12345, "tid":14, "ts":1748104828426427, "dur":250, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":14, "ts":1748104828426686, "dur":177, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Services.Core.Networking.dll.mvfrm" }}
,{ "pid":12345, "tid":14, "ts":1748104828426864, "dur":94, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":14, "ts":1748104828426974, "dur":145, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.DiagnosticsModule.dll" }}
,{ "pid":12345, "tid":14, "ts":1748104828427660, "dur":78, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Text.Encoding.Extensions.dll" }}
,{ "pid":12345, "tid":14, "ts":1748104828426965, "dur":966, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Services.Core.Networking.dll (+2 others)" }}
,{ "pid":12345, "tid":14, "ts":1748104828427932, "dur":139, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":14, "ts":1748104828428071, "dur":191, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Services.Core.Networking.dll (+2 others)" }}
,{ "pid":12345, "tid":14, "ts":1748104828428273, "dur":188, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":14, "ts":1748104828428791, "dur":138, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Diagnostics.FileVersionInfo.dll" }}
,{ "pid":12345, "tid":14, "ts":1748104828429180, "dur":230, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Xml.XmlSerializer.dll" }}
,{ "pid":12345, "tid":14, "ts":1748104828428475, "dur":1076, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VSCode.Editor.dll (+2 others)" }}
,{ "pid":12345, "tid":14, "ts":1748104828429551, "dur":159, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":14, "ts":1748104828429777, "dur":243, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":14, "ts":1748104828430031, "dur":117, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":14, "ts":1748104828430159, "dur":95, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":14, "ts":1748104828430339, "dur":128, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":14, "ts":1748104828430484, "dur":97, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":14, "ts":1748104828430592, "dur":95, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":14, "ts":1748104828430694, "dur":217, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":14, "ts":1748104828430922, "dur":199, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":14, "ts":1748104828431132, "dur":119, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":14, "ts":1748104828431284, "dur":161, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":14, "ts":1748104828431455, "dur":112, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":14, "ts":1748104828431597, "dur":128, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":14, "ts":1748104828431740, "dur":377, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":14, "ts":1748104828432126, "dur":104, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":14, "ts":1748104828432238, "dur":276, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":14, "ts":1748104828432525, "dur":1187, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":14, "ts":1748104828433712, "dur":160, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":14, "ts":1748104828433873, "dur":954, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":14, "ts":1748104828434828, "dur":97034, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1748104828358936, "dur":31409, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1748104828390366, "dur":97, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.TLSModule.dll" }}
,{ "pid":12345, "tid":15, "ts":1748104828390465, "dur":386, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe" }}
,{ "pid":12345, "tid":15, "ts":1748104828390351, "dur":502, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TLSModule.dll_DE20F4B1419B4301.mvfrm" }}
,{ "pid":12345, "tid":15, "ts":1748104828390917, "dur":90, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.SubsystemsModule.dll" }}
,{ "pid":12345, "tid":15, "ts":1748104828390914, "dur":97, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubsystemsModule.dll_0DD307A4E1F3A572.mvfrm" }}
,{ "pid":12345, "tid":15, "ts":1748104828391011, "dur":53, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1748104828391075, "dur":194, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.CoreModule.dll" }}
,{ "pid":12345, "tid":15, "ts":1748104828391072, "dur":202, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreModule.dll_3930B16E78AA3822.mvfrm" }}
,{ "pid":12345, "tid":15, "ts":1748104828391274, "dur":123, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1748104828391408, "dur":247, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.AudioModule.dll" }}
,{ "pid":12345, "tid":15, "ts":1748104828391406, "dur":253, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AudioModule.dll_42BCEF521E66AF1C.mvfrm" }}
,{ "pid":12345, "tid":15, "ts":1748104828391660, "dur":64, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1748104828391733, "dur":51, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VFXModule.dll_5EFF8D201808AC6E.mvfrm" }}
,{ "pid":12345, "tid":15, "ts":1748104828391785, "dur":155, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1748104828391954, "dur":58, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEditor.Graphs.dll" }}
,{ "pid":12345, "tid":15, "ts":1748104828391952, "dur":63, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Graphs.dll_812F6278076A2054.mvfrm" }}
,{ "pid":12345, "tid":15, "ts":1748104828392077, "dur":189, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.rsp2" }}
,{ "pid":12345, "tid":15, "ts":1748104828392267, "dur":90, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1748104828392364, "dur":312, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Services.Core.Internal.dll.mvfrm.rsp" }}
,{ "pid":12345, "tid":15, "ts":1748104828392677, "dur":59, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1748104828392743, "dur":175, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteText Library/Bee/artifacts/1900b0aE.dag/UnityGLTF.Helpers.rsp" }}
,{ "pid":12345, "tid":15, "ts":1748104828392919, "dur":76, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1748104828393007, "dur":50, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1748104828393119, "dur":52, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1748104828393171, "dur":106, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.rsp2" }}
,{ "pid":12345, "tid":15, "ts":1748104828393279, "dur":312, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Services.Core.Telemetry.rsp" }}
,{ "pid":12345, "tid":15, "ts":1748104828393592, "dur":79, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1748104828393678, "dur":85, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1748104828393771, "dur":58, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1748104828393836, "dur":122, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteText Library/Bee/artifacts/1900b0aE.dag/UnityGLTFEditor.dll.mvfrm.rsp" }}
,{ "pid":12345, "tid":15, "ts":1748104828394010, "dur":357, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.XR.LegacyInputHelpers.rsp" }}
,{ "pid":12345, "tid":15, "ts":1748104828394368, "dur":68, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1748104828394436, "dur":315, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.XR.LegacyInputHelpers.rsp" }}
,{ "pid":12345, "tid":15, "ts":1748104828394754, "dur":50, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1748104828394925, "dur":299, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll.mvfrm.rsp" }}
,{ "pid":12345, "tid":15, "ts":1748104828395238, "dur":53, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1748104828395301, "dur":196, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteText Library/Bee/artifacts/1900b0aE.dag/UnityGLTF.Interactivity.Runtime.rsp" }}
,{ "pid":12345, "tid":15, "ts":1748104828395498, "dur":51, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1748104828395555, "dur":183, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VSCode.Editor.rsp" }}
,{ "pid":12345, "tid":15, "ts":1748104828395739, "dur":51, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1748104828395797, "dur":194, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteText Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.rsp" }}
,{ "pid":12345, "tid":15, "ts":1748104828395992, "dur":92, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1748104828396091, "dur":795, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.services.core@1.12.5\\Editor\\Core\\Environments\\EnvironmentsClient.cs" }}
,{ "pid":12345, "tid":15, "ts":1748104828396091, "dur":1770, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1748104828400034, "dur":791, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.ide.rider@3.0.31\\Rider\\Editor\\Properties\\AssemblyInfo.cs" }}
,{ "pid":12345, "tid":15, "ts":1748104828401503, "dur":632, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.ide.rider@3.0.31\\Rider\\Editor\\ProjectGeneration\\LastWriteTracker.cs" }}
,{ "pid":12345, "tid":15, "ts":1748104828402227, "dur":1061, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.ide.rider@3.0.31\\Rider\\Editor\\ProjectGeneration\\IGenerator.cs" }}
,{ "pid":12345, "tid":15, "ts":1748104828397862, "dur":5634, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1748104828403497, "dur":843, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1748104828404385, "dur":1087, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.shadergraph@14.0.11\\Editor\\Generation\\Collections\\AssetCollection.cs" }}
,{ "pid":12345, "tid":15, "ts":1748104828404341, "dur":2005, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1748104828406346, "dur":831, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1748104828407177, "dur":831, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1748104828408375, "dur":763, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\org.khronos.unitygltf@1d3d93121c\\Runtime\\Plugins\\GLTFSerialization\\Extensions\\MSFT_lodFactory.cs" }}
,{ "pid":12345, "tid":15, "ts":1748104828408009, "dur":1631, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1748104828409641, "dur":1028, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1748104828411025, "dur":1404, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.timeline@1.7.6\\Editor\\treeview\\Drawers\\TrackDrawer.cs" }}
,{ "pid":12345, "tid":15, "ts":1748104828410670, "dur":2531, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1748104828413202, "dur":2545, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1748104828415747, "dur":1956, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1748104828417708, "dur":1264, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEditor.TestRunner\\TestRunner\\EditorEnumeratorTestWorkItem.cs" }}
,{ "pid":12345, "tid":15, "ts":1748104828417708, "dur":2579, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1748104828420288, "dur":1251, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1748104828421539, "dur":840, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1748104828422379, "dur":92, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1748104828422472, "dur":188, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.dll.mvfrm" }}
,{ "pid":12345, "tid":15, "ts":1748104828422660, "dur":58, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1748104828422729, "dur":826, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.dll (+2 others)" }}
,{ "pid":12345, "tid":15, "ts":1748104828423556, "dur":366, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1748104828423943, "dur":283, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1748104828424234, "dur":220, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm" }}
,{ "pid":12345, "tid":15, "ts":1748104828424455, "dur":157, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1748104828424624, "dur":117, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.DeviceSimulatorModule.dll" }}
,{ "pid":12345, "tid":15, "ts":1748104828425370, "dur":81, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\System.Numerics.dll" }}
,{ "pid":12345, "tid":15, "ts":1748104828425472, "dur":355, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\PackageCache\\com.unity.ext.nunit@1.0.6\\net35\\unity-custom\\nunit.framework.dll" }}
,{ "pid":12345, "tid":15, "ts":1748104828425924, "dur":1143, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\PackageCache\\com.unity.ide.visualstudio@2.0.22\\Editor\\Symbols.cs" }}
,{ "pid":12345, "tid":15, "ts":1748104828424617, "dur":2512, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll (+2 others)" }}
,{ "pid":12345, "tid":15, "ts":1748104828427130, "dur":290, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1748104828427433, "dur":175, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll.mvfrm" }}
,{ "pid":12345, "tid":15, "ts":1748104828427609, "dur":80, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1748104828427697, "dur":871, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll (+2 others)" }}
,{ "pid":12345, "tid":15, "ts":1748104828428569, "dur":223, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1748104828429122, "dur":210, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityGLTF.Interactivity.Runtime.dll.mvfrm" }}
,{ "pid":12345, "tid":15, "ts":1748104828429333, "dur":55, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1748104828429459, "dur":182, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ClusterRendererModule.dll" }}
,{ "pid":12345, "tid":15, "ts":1748104828430274, "dur":52, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\PackageCache\\org.khronos.unitygltf@1d3d93121c\\Runtime\\Scripts\\Interactivity\\Schema\\Nodes\\Animation\\Animation_StartNode.cs" }}
,{ "pid":12345, "tid":15, "ts":1748104828429398, "dur":1049, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1900b0aE.dag/UnityGLTF.Interactivity.Runtime.dll (+2 others)" }}
,{ "pid":12345, "tid":15, "ts":1748104828430448, "dur":163, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1748104828430625, "dur":55, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1748104828430691, "dur":478, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1748104828431177, "dur":445, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1748104828431639, "dur":97, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1748104828431745, "dur":282, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1748104828432055, "dur":114, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1748104828432178, "dur":86, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1748104828432276, "dur":117, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1748104828432415, "dur":110, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1748104828432526, "dur":1176, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1748104828433702, "dur":184, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1748104828433886, "dur":943, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1748104828434835, "dur":97039, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":16, "ts":1748104828358985, "dur":31372, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":16, "ts":1748104828390374, "dur":52, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.TilemapModule.dll" }}
,{ "pid":12345, "tid":16, "ts":1748104828390427, "dur":381, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe" }}
,{ "pid":12345, "tid":16, "ts":1748104828390358, "dur":451, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TilemapModule.dll_83DA5D6B18EEBF64.mvfrm" }}
,{ "pid":12345, "tid":16, "ts":1748104828390810, "dur":96, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":16, "ts":1748104828390922, "dur":96, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.SubstanceModule.dll" }}
,{ "pid":12345, "tid":16, "ts":1748104828390920, "dur":102, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubstanceModule.dll_97711FF90AF13DF6.mvfrm" }}
,{ "pid":12345, "tid":16, "ts":1748104828391022, "dur":55, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":16, "ts":1748104828391086, "dur":50, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.DeviceSimulatorModule.dll" }}
,{ "pid":12345, "tid":16, "ts":1748104828391084, "dur":55, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DeviceSimulatorModule.dll_482DD5D80B96BD3F.mvfrm" }}
,{ "pid":12345, "tid":16, "ts":1748104828391213, "dur":133, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.TextCoreFontEngineModule.dll" }}
,{ "pid":12345, "tid":16, "ts":1748104828391211, "dur":138, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreFontEngineModule.dll_2C7313E0A5143B60.mvfrm" }}
,{ "pid":12345, "tid":16, "ts":1748104828391350, "dur":84, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":16, "ts":1748104828391442, "dur":53, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ContentLoadModule.dll_5F57895F8E736105.mvfrm" }}
,{ "pid":12345, "tid":16, "ts":1748104828391548, "dur":139, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.GIModule.dll" }}
,{ "pid":12345, "tid":16, "ts":1748104828391546, "dur":145, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GIModule.dll_D627199384663E50.mvfrm" }}
,{ "pid":12345, "tid":16, "ts":1748104828391692, "dur":54, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":16, "ts":1748104828391752, "dur":51, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VRModule.dll_8C309538E6B8E15A.mvfrm" }}
,{ "pid":12345, "tid":16, "ts":1748104828391803, "dur":147, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":16, "ts":1748104828391964, "dur":204, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.UnityAdditionalFile.txt" }}
,{ "pid":12345, "tid":16, "ts":1748104828392169, "dur":55, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":16, "ts":1748104828392235, "dur":151, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Services.Core.rsp2" }}
,{ "pid":12345, "tid":16, "ts":1748104828392387, "dur":76, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":16, "ts":1748104828392469, "dur":187, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.UnityAdditionalFile.txt" }}
,{ "pid":12345, "tid":16, "ts":1748104828392657, "dur":53, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":16, "ts":1748104828392718, "dur":238, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Services.Core.Environments.rsp2" }}
,{ "pid":12345, "tid":16, "ts":1748104828392957, "dur":104, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":16, "ts":1748104828393086, "dur":122, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":16, "ts":1748104828393231, "dur":135, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Services.Core.Device.dll.mvfrm.rsp" }}
,{ "pid":12345, "tid":16, "ts":1748104828393367, "dur":78, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":16, "ts":1748104828393452, "dur":229, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.rsp" }}
,{ "pid":12345, "tid":16, "ts":1748104828393683, "dur":81, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":16, "ts":1748104828393775, "dur":56, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":16, "ts":1748104828393852, "dur":75, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":16, "ts":1748104828393933, "dur":489, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.rsp" }}
,{ "pid":12345, "tid":16, "ts":1748104828394464, "dur":673, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.rsp" }}
,{ "pid":12345, "tid":16, "ts":1748104828395147, "dur":56, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":16, "ts":1748104828395273, "dur":167, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.rsp" }}
,{ "pid":12345, "tid":16, "ts":1748104828395441, "dur":60, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":16, "ts":1748104828395528, "dur":53, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Sprite.Editor.rsp" }}
,{ "pid":12345, "tid":16, "ts":1748104828395582, "dur":60, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":16, "ts":1748104828395711, "dur":52, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":16, "ts":1748104828395763, "dur":87, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/1900b0aE.dag/Autodesk.Fbx.Editor.dll.mvfrm.rsp" }}
,{ "pid":12345, "tid":16, "ts":1748104828395899, "dur":131, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/1900b0aE.dag/FbxBuildTestAssets.UnityAdditionalFile.txt" }}
,{ "pid":12345, "tid":16, "ts":1748104828396063, "dur":64, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":16, "ts":1748104828396132, "dur":1624, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":16, "ts":1748104828400010, "dur":806, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.ide.rider@3.0.31\\Rider\\Editor\\UnitTesting\\SyncTestRunCallback.cs" }}
,{ "pid":12345, "tid":16, "ts":1748104828397757, "dur":3059, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":16, "ts":1748104828401692, "dur":545, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.2d.tilemap@1.0.0\\Editor\\EditorTools\\SelectTool.cs" }}
,{ "pid":12345, "tid":16, "ts":1748104828402897, "dur":689, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.2d.tilemap@1.0.0\\Editor\\EditorTools\\GridSelection\\GridSelectionTransformTool.cs" }}
,{ "pid":12345, "tid":16, "ts":1748104828403587, "dur":1705, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.2d.tilemap@1.0.0\\Editor\\EditorTools\\GridSelection\\GridSelectionToolEditor.cs" }}
,{ "pid":12345, "tid":16, "ts":1748104828400817, "dur":5262, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":16, "ts":1748104828406079, "dur":1012, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":16, "ts":1748104828407092, "dur":1212, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":16, "ts":1748104828408388, "dur":773, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.services.core@1.12.5\\Runtime\\Configuration\\ConfigurationCollectionHelper.cs" }}
,{ "pid":12345, "tid":16, "ts":1748104828408305, "dur":1712, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":16, "ts":1748104828410017, "dur":908, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":16, "ts":1748104828411479, "dur":827, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.timeline@1.7.6\\Editor\\inspectors\\TrackAssetInspector.cs" }}
,{ "pid":12345, "tid":16, "ts":1748104828410925, "dur":1704, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":16, "ts":1748104828412629, "dur":881, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":16, "ts":1748104828413511, "dur":2501, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":16, "ts":1748104828416013, "dur":2561, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":16, "ts":1748104828418574, "dur":828, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":16, "ts":1748104828419598, "dur":1896, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":16, "ts":1748104828421535, "dur":116, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":16, "ts":1748104828421661, "dur":694, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":16, "ts":1748104828422356, "dur":96, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":16, "ts":1748104828422454, "dur":175, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll.mvfrm" }}
,{ "pid":12345, "tid":16, "ts":1748104828422630, "dur":76, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":16, "ts":1748104828422740, "dur":179, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.SceneTemplateModule.dll" }}
,{ "pid":12345, "tid":16, "ts":1748104828423283, "dur":67, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Globalization.Calendars.dll" }}
,{ "pid":12345, "tid":16, "ts":1748104828423406, "dur":506, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.NetworkInformation.dll" }}
,{ "pid":12345, "tid":16, "ts":1748104828424208, "dur":277, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Threading.Overlapped.dll" }}
,{ "pid":12345, "tid":16, "ts":1748104828424561, "dur":71, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\PackageCache\\com.unity.timeline@1.7.6\\Runtime\\Activation\\ActivationMixerPlayable.cs" }}
,{ "pid":12345, "tid":16, "ts":1748104828424803, "dur":284, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\PackageCache\\com.unity.timeline@1.7.6\\Runtime\\Utilities\\FrameRate.cs" }}
,{ "pid":12345, "tid":16, "ts":1748104828422714, "dur":2408, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll (+2 others)" }}
,{ "pid":12345, "tid":16, "ts":1748104828425123, "dur":137, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":16, "ts":1748104828425336, "dur":286, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityGLTFScripts.dll.mvfrm" }}
,{ "pid":12345, "tid":16, "ts":1748104828425623, "dur":71, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":16, "ts":1748104828425706, "dur":701, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll (+2 others)" }}
,{ "pid":12345, "tid":16, "ts":1748104828426407, "dur":184, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":16, "ts":1748104828426615, "dur":415, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.dll" }}
,{ "pid":12345, "tid":16, "ts":1748104828427309, "dur":50, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.FileVersionInfo.dll" }}
,{ "pid":12345, "tid":16, "ts":1748104828427660, "dur":94, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\PackageCache\\com.unity.analytics@3.8.1\\Unity.Analytics.Editor.dll" }}
,{ "pid":12345, "tid":16, "ts":1748104828426604, "dur":1224, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Services.Core.Threading.dll (+2 others)" }}
,{ "pid":12345, "tid":16, "ts":1748104828427829, "dur":655, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":16, "ts":1748104828428498, "dur":87, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":16, "ts":1748104828428586, "dur":68, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Services.Core.Threading.ref.dll_F1D7894C57B2A611.mvfrm" }}
,{ "pid":12345, "tid":16, "ts":1748104828428657, "dur":231, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Analytics.dll.mvfrm" }}
,{ "pid":12345, "tid":16, "ts":1748104828428889, "dur":51, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":16, "ts":1748104828429380, "dur":125, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\00ruanjian\\00unity\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityAnalyticsCommonModule.dll" }}
,{ "pid":12345, "tid":16, "ts":1748104828429219, "dur":732, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1900b0aE.dag/Analytics.dll (+2 others)" }}
,{ "pid":12345, "tid":16, "ts":1748104828429952, "dur":171, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":16, "ts":1748104828430146, "dur":240, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":16, "ts":1748104828430396, "dur":82, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":16, "ts":1748104828430488, "dur":354, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":16, "ts":1748104828430843, "dur":88, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles Library/ScriptAssemblies/Unity.TextMeshPro.dll" }}
,{ "pid":12345, "tid":16, "ts":1748104828430936, "dur":184, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":16, "ts":1748104828431121, "dur":59, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles Library/ScriptAssemblies/McpUnity.Editor.pdb" }}
,{ "pid":12345, "tid":16, "ts":1748104828431229, "dur":243, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":16, "ts":1748104828431481, "dur":129, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":16, "ts":1748104828431622, "dur":100, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":16, "ts":1748104828431732, "dur":96, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":16, "ts":1748104828431842, "dur":203, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":16, "ts":1748104828432060, "dur":89, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":16, "ts":1748104828432158, "dur":210, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":16, "ts":1748104828432372, "dur":138, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":16, "ts":1748104828432510, "dur":843, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":16, "ts":1748104828433357, "dur":139, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":16, "ts":1748104828433501, "dur":117, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":16, "ts":1748104828433619, "dur":175, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":16, "ts":1748104828433795, "dur":1004, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":16, "ts":1748104828434807, "dur":118, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":16, "ts":1748104828434933, "dur":96947, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1748104829001992, "dur":7949, "ph":"X", "name": "ProfilerWriteOutput" }
,