{"format": 1, "restore": {"D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Assembly-CSharp-Editor.csproj": {}}, "projects": {"D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Analytics.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Analytics.csproj", "projectName": "Analytics", "projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Analytics.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Temp\\obj\\Debug\\Analytics\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEditor.TestRunner.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEditor.TestRunner.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEditor.UI.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEditor.UI.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEngine.TestRunner.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEngine.TestRunner.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEngine.UI.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEngine.UI.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.203\\RuntimeIdentifierGraph.json"}}}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Assembly-CSharp-Editor.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Assembly-CSharp-Editor.csproj", "projectName": "Assembly-CSharp-Editor", "projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Assembly-CSharp-Editor.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Temp\\obj\\Debug\\Assembly-CSharp-Editor\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Analytics.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Analytics.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Assembly-CSharp.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Assembly-CSharp.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Autodesk.Fbx.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Autodesk.Fbx.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Autodesk.Fbx.Editor.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Autodesk.Fbx.Editor.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\GLTFSerialization.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\GLTFSerialization.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\McpUnity.Editor.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\McpUnity.Editor.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.2D.Sprite.Editor.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.2D.Sprite.Editor.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.2D.Tilemap.Editor.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.2D.Tilemap.Editor.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.AI.Navigation.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.AI.Navigation.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.AI.Navigation.Editor.ConversionSystem.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.AI.Navigation.Editor.ConversionSystem.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.AI.Navigation.Editor.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.AI.Navigation.Editor.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.AI.Navigation.Updater.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.AI.Navigation.Updater.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.Analytics.DataPrivacy.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.Analytics.DataPrivacy.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.EditorCoroutines.Editor.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.EditorCoroutines.Editor.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.Formats.Fbx.Editor.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.Formats.Fbx.Editor.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.Formats.Fbx.Runtime.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.Formats.Fbx.Runtime.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.Mathematics.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.Mathematics.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.Mathematics.Editor.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.Mathematics.Editor.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.PlasticSCM.Editor.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.PlasticSCM.Editor.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.RenderPipelines.Core.Editor.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.RenderPipelines.Core.Editor.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.RenderPipelines.Core.Runtime.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.RenderPipelines.Core.Runtime.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.RenderPipelines.Core.ShaderLibrary.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.RenderPipelines.Core.ShaderLibrary.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.Rider.Editor.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.Rider.Editor.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.Searcher.Editor.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.Searcher.Editor.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.Services.Analytics.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.Services.Analytics.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.Services.Analytics.Editor.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.Services.Analytics.Editor.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.Services.Core.Analytics.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.Services.Core.Analytics.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.Services.Core.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.Services.Core.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.Services.Core.Environments.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.Services.Core.Environments.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.ShaderGraph.Editor.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.ShaderGraph.Editor.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.TextMeshPro.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.TextMeshPro.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.TextMeshPro.Editor.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.TextMeshPro.Editor.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.Timeline.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.Timeline.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.Timeline.Editor.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.Timeline.Editor.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.VisualStudio.Editor.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.VisualStudio.Editor.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.VSCode.Editor.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.VSCode.Editor.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEditor.SpatialTracking.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEditor.SpatialTracking.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEditor.TestRunner.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEditor.TestRunner.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEditor.UI.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEditor.UI.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEditor.XR.LegacyInputHelpers.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEditor.XR.LegacyInputHelpers.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEngine.Advertisements.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEngine.Advertisements.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEngine.Advertisements.Editor.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEngine.Advertisements.Editor.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEngine.SpatialTracking.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEngine.SpatialTracking.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEngine.TestRunner.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEngine.TestRunner.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEngine.UI.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEngine.UI.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEngine.XR.LegacyInputHelpers.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEngine.XR.LegacyInputHelpers.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityGLTF.Interactivity.Runtime.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityGLTF.Interactivity.Runtime.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityGLTF.Interactivity.VisualScriptingInstall.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityGLTF.Interactivity.VisualScriptingInstall.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityGLTFEditor.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityGLTFEditor.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityGLTFScripts.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityGLTFScripts.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.203\\RuntimeIdentifierGraph.json"}}}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Assembly-CSharp.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Assembly-CSharp.csproj", "projectName": "Assembly-CSharp", "projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Assembly-CSharp.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Temp\\obj\\Debug\\Assembly-CSharp\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Analytics.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Analytics.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Autodesk.Fbx.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Autodesk.Fbx.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Autodesk.Fbx.Editor.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Autodesk.Fbx.Editor.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\GLTFSerialization.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\GLTFSerialization.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\McpUnity.Editor.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\McpUnity.Editor.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.2D.Sprite.Editor.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.2D.Sprite.Editor.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.2D.Tilemap.Editor.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.2D.Tilemap.Editor.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.AI.Navigation.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.AI.Navigation.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.AI.Navigation.Editor.ConversionSystem.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.AI.Navigation.Editor.ConversionSystem.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.AI.Navigation.Editor.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.AI.Navigation.Editor.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.AI.Navigation.Updater.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.AI.Navigation.Updater.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.Analytics.DataPrivacy.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.Analytics.DataPrivacy.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.EditorCoroutines.Editor.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.EditorCoroutines.Editor.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.Formats.Fbx.Editor.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.Formats.Fbx.Editor.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.Formats.Fbx.Runtime.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.Formats.Fbx.Runtime.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.Mathematics.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.Mathematics.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.Mathematics.Editor.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.Mathematics.Editor.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.PlasticSCM.Editor.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.PlasticSCM.Editor.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.RenderPipelines.Core.Editor.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.RenderPipelines.Core.Editor.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.RenderPipelines.Core.Runtime.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.RenderPipelines.Core.Runtime.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.RenderPipelines.Core.ShaderLibrary.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.RenderPipelines.Core.ShaderLibrary.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.Rider.Editor.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.Rider.Editor.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.Searcher.Editor.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.Searcher.Editor.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.Services.Analytics.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.Services.Analytics.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.Services.Analytics.Editor.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.Services.Analytics.Editor.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.Services.Core.Analytics.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.Services.Core.Analytics.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.Services.Core.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.Services.Core.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.Services.Core.Environments.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.Services.Core.Environments.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.ShaderGraph.Editor.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.ShaderGraph.Editor.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.TextMeshPro.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.TextMeshPro.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.TextMeshPro.Editor.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.TextMeshPro.Editor.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.Timeline.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.Timeline.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.Timeline.Editor.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.Timeline.Editor.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.VisualStudio.Editor.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.VisualStudio.Editor.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.VSCode.Editor.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.VSCode.Editor.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEditor.SpatialTracking.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEditor.SpatialTracking.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEditor.UI.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEditor.UI.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEditor.XR.LegacyInputHelpers.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEditor.XR.LegacyInputHelpers.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEngine.Advertisements.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEngine.Advertisements.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEngine.Advertisements.Editor.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEngine.Advertisements.Editor.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEngine.SpatialTracking.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEngine.SpatialTracking.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEngine.UI.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEngine.UI.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEngine.XR.LegacyInputHelpers.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEngine.XR.LegacyInputHelpers.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityGLTF.Interactivity.Runtime.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityGLTF.Interactivity.Runtime.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityGLTF.Interactivity.VisualScriptingInstall.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityGLTF.Interactivity.VisualScriptingInstall.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityGLTFEditor.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityGLTFEditor.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityGLTFScripts.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityGLTFScripts.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.203\\RuntimeIdentifierGraph.json"}}}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Autodesk.Fbx.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Autodesk.Fbx.csproj", "projectName": "Autodesk.Fbx", "projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Autodesk.Fbx.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Temp\\obj\\Debug\\Autodesk.Fbx\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEditor.UI.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEditor.UI.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEngine.UI.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEngine.UI.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.203\\RuntimeIdentifierGraph.json"}}}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Autodesk.Fbx.Editor.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Autodesk.Fbx.Editor.csproj", "projectName": "Autodesk.Fbx.Editor", "projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Autodesk.Fbx.Editor.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Temp\\obj\\Debug\\Autodesk.Fbx.Editor\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEditor.TestRunner.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEditor.TestRunner.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEditor.UI.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEditor.UI.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEngine.TestRunner.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEngine.TestRunner.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEngine.UI.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEngine.UI.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.203\\RuntimeIdentifierGraph.json"}}}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\GLTFSerialization.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\GLTFSerialization.csproj", "projectName": "GLTFSerialization", "projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\GLTFSerialization.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Temp\\obj\\Debug\\GLTFSerialization\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.Mathematics.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.Mathematics.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEditor.UI.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEditor.UI.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEngine.UI.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEngine.UI.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.203\\RuntimeIdentifierGraph.json"}}}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\McpUnity.Editor.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\McpUnity.Editor.csproj", "projectName": "McpUnity.Editor", "projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\McpUnity.Editor.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Temp\\obj\\Debug\\McpUnity.Editor\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.EditorCoroutines.Editor.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.EditorCoroutines.Editor.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEditor.TestRunner.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEditor.TestRunner.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEditor.UI.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEditor.UI.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEngine.TestRunner.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEngine.TestRunner.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEngine.UI.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEngine.UI.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.203\\RuntimeIdentifierGraph.json"}}}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.2D.Sprite.Editor.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.2D.Sprite.Editor.csproj", "projectName": "Unity.2D.Sprite.Editor", "projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.2D.Sprite.Editor.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Temp\\obj\\Debug\\Unity.2D.Sprite.Editor\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEditor.TestRunner.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEditor.TestRunner.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEditor.UI.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEditor.UI.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEngine.TestRunner.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEngine.TestRunner.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEngine.UI.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEngine.UI.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.203\\RuntimeIdentifierGraph.json"}}}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.2D.Tilemap.Editor.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.2D.Tilemap.Editor.csproj", "projectName": "Unity.2D.Tilemap.Editor", "projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.2D.Tilemap.Editor.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Temp\\obj\\Debug\\Unity.2D.Tilemap.Editor\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEditor.TestRunner.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEditor.TestRunner.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEditor.UI.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEditor.UI.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEngine.TestRunner.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEngine.TestRunner.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEngine.UI.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEngine.UI.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.203\\RuntimeIdentifierGraph.json"}}}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.AI.Navigation.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.AI.Navigation.csproj", "projectName": "Unity.AI.Navigation", "projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.AI.Navigation.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Temp\\obj\\Debug\\Unity.AI.Navigation\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEditor.UI.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEditor.UI.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEngine.UI.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEngine.UI.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.203\\RuntimeIdentifierGraph.json"}}}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.AI.Navigation.Editor.ConversionSystem.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.AI.Navigation.Editor.ConversionSystem.csproj", "projectName": "Unity.AI.Navigation.Editor.ConversionSystem", "projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.AI.Navigation.Editor.ConversionSystem.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Temp\\obj\\Debug\\Unity.AI.Navigation.Editor.ConversionSystem\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEditor.TestRunner.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEditor.TestRunner.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEditor.UI.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEditor.UI.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEngine.TestRunner.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEngine.TestRunner.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEngine.UI.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEngine.UI.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.203\\RuntimeIdentifierGraph.json"}}}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.AI.Navigation.Editor.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.AI.Navigation.Editor.csproj", "projectName": "Unity.AI.Navigation.Editor", "projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.AI.Navigation.Editor.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Temp\\obj\\Debug\\Unity.AI.Navigation.Editor\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.AI.Navigation.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.AI.Navigation.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.AI.Navigation.Editor.ConversionSystem.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.AI.Navigation.Editor.ConversionSystem.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEditor.TestRunner.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEditor.TestRunner.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEditor.UI.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEditor.UI.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEngine.TestRunner.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEngine.TestRunner.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEngine.UI.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEngine.UI.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.203\\RuntimeIdentifierGraph.json"}}}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.AI.Navigation.Updater.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.AI.Navigation.Updater.csproj", "projectName": "Unity.AI.Navigation.Updater", "projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.AI.Navigation.Updater.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Temp\\obj\\Debug\\Unity.AI.Navigation.Updater\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.AI.Navigation.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.AI.Navigation.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.AI.Navigation.Editor.ConversionSystem.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.AI.Navigation.Editor.ConversionSystem.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.AI.Navigation.Editor.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.AI.Navigation.Editor.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEditor.TestRunner.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEditor.TestRunner.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEditor.UI.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEditor.UI.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEngine.TestRunner.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEngine.TestRunner.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEngine.UI.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEngine.UI.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.203\\RuntimeIdentifierGraph.json"}}}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.Analytics.DataPrivacy.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.Analytics.DataPrivacy.csproj", "projectName": "Unity.Analytics.DataPrivacy", "projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.Analytics.DataPrivacy.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Temp\\obj\\Debug\\Unity.Analytics.DataPrivacy\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEditor.UI.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEditor.UI.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEngine.UI.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEngine.UI.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.203\\RuntimeIdentifierGraph.json"}}}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.EditorCoroutines.Editor.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.EditorCoroutines.Editor.csproj", "projectName": "Unity.EditorCoroutines.Editor", "projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.EditorCoroutines.Editor.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Temp\\obj\\Debug\\Unity.EditorCoroutines.Editor\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEditor.TestRunner.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEditor.TestRunner.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEditor.UI.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEditor.UI.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEngine.TestRunner.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEngine.TestRunner.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEngine.UI.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEngine.UI.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.203\\RuntimeIdentifierGraph.json"}}}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.Formats.Fbx.Editor.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.Formats.Fbx.Editor.csproj", "projectName": "Unity.Formats.Fbx.Editor", "projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.Formats.Fbx.Editor.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Temp\\obj\\Debug\\Unity.Formats.Fbx.Editor\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Autodesk.Fbx.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Autodesk.Fbx.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.Formats.Fbx.Runtime.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.Formats.Fbx.Runtime.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.Timeline.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.Timeline.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.Timeline.Editor.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.Timeline.Editor.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEditor.TestRunner.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEditor.TestRunner.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEditor.UI.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEditor.UI.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEngine.TestRunner.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEngine.TestRunner.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEngine.UI.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEngine.UI.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.203\\RuntimeIdentifierGraph.json"}}}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.Formats.Fbx.Runtime.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.Formats.Fbx.Runtime.csproj", "projectName": "Unity.Formats.Fbx.Runtime", "projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.Formats.Fbx.Runtime.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Temp\\obj\\Debug\\Unity.Formats.Fbx.Runtime\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEditor.UI.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEditor.UI.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEngine.UI.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEngine.UI.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.203\\RuntimeIdentifierGraph.json"}}}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.Mathematics.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.Mathematics.csproj", "projectName": "Unity.Mathematics", "projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.Mathematics.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Temp\\obj\\Debug\\Unity.Mathematics\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEditor.UI.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEditor.UI.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEngine.UI.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEngine.UI.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.203\\RuntimeIdentifierGraph.json"}}}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.Mathematics.Editor.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.Mathematics.Editor.csproj", "projectName": "Unity.Mathematics.Editor", "projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.Mathematics.Editor.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Temp\\obj\\Debug\\Unity.Mathematics.Editor\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.Mathematics.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.Mathematics.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEditor.TestRunner.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEditor.TestRunner.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEditor.UI.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEditor.UI.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEngine.TestRunner.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEngine.TestRunner.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEngine.UI.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEngine.UI.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.203\\RuntimeIdentifierGraph.json"}}}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.PlasticSCM.Editor.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.PlasticSCM.Editor.csproj", "projectName": "Unity.PlasticSCM.Editor", "projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.PlasticSCM.Editor.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Temp\\obj\\Debug\\Unity.PlasticSCM.Editor\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEditor.TestRunner.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEditor.TestRunner.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEditor.UI.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEditor.UI.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEngine.TestRunner.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEngine.TestRunner.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEngine.UI.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEngine.UI.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.203\\RuntimeIdentifierGraph.json"}}}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.RenderPipelines.Core.Editor.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.RenderPipelines.Core.Editor.csproj", "projectName": "Unity.RenderPipelines.Core.Editor", "projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.RenderPipelines.Core.Editor.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Temp\\obj\\Debug\\Unity.RenderPipelines.Core.Editor\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.RenderPipelines.Core.Runtime.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.RenderPipelines.Core.Runtime.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEditor.TestRunner.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEditor.TestRunner.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEditor.UI.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEditor.UI.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEngine.TestRunner.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEngine.TestRunner.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEngine.UI.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEngine.UI.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.203\\RuntimeIdentifierGraph.json"}}}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.RenderPipelines.Core.Runtime.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.RenderPipelines.Core.Runtime.csproj", "projectName": "Unity.RenderPipelines.Core.Runtime", "projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.RenderPipelines.Core.Runtime.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Temp\\obj\\Debug\\Unity.RenderPipelines.Core.Runtime\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEditor.UI.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEditor.UI.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEngine.UI.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEngine.UI.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.203\\RuntimeIdentifierGraph.json"}}}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.RenderPipelines.Core.ShaderLibrary.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.RenderPipelines.Core.ShaderLibrary.csproj", "projectName": "Unity.RenderPipelines.Core.ShaderLibrary", "projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.RenderPipelines.Core.ShaderLibrary.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Temp\\obj\\Debug\\Unity.RenderPipelines.Core.ShaderLibrary\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEditor.UI.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEditor.UI.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEngine.UI.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEngine.UI.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.203\\RuntimeIdentifierGraph.json"}}}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.csproj", "projectName": "Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary", "projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Temp\\obj\\Debug\\Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEditor.UI.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEditor.UI.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEngine.UI.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEngine.UI.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.203\\RuntimeIdentifierGraph.json"}}}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.Rider.Editor.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.Rider.Editor.csproj", "projectName": "Unity.Rider.Editor", "projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.Rider.Editor.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Temp\\obj\\Debug\\Unity.Rider.Editor\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEditor.TestRunner.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEditor.TestRunner.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEditor.UI.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEditor.UI.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEngine.TestRunner.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEngine.TestRunner.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEngine.UI.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEngine.UI.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.203\\RuntimeIdentifierGraph.json"}}}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.Searcher.Editor.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.Searcher.Editor.csproj", "projectName": "Unity.Searcher.Editor", "projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.Searcher.Editor.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Temp\\obj\\Debug\\Unity.Searcher.Editor\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEditor.TestRunner.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEditor.TestRunner.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEditor.UI.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEditor.UI.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEngine.TestRunner.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEngine.TestRunner.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEngine.UI.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEngine.UI.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.203\\RuntimeIdentifierGraph.json"}}}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.Services.Analytics.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.Services.Analytics.csproj", "projectName": "Unity.Services.Analytics", "projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.Services.Analytics.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Temp\\obj\\Debug\\Unity.Services.Analytics\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.Services.Core.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.Services.Core.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.Services.Core.Internal.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.Services.Core.Internal.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEditor.UI.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEditor.UI.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEngine.UI.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEngine.UI.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.203\\RuntimeIdentifierGraph.json"}}}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.Services.Analytics.Editor.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.Services.Analytics.Editor.csproj", "projectName": "Unity.Services.Analytics.Editor", "projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.Services.Analytics.Editor.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Temp\\obj\\Debug\\Unity.Services.Analytics.Editor\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.Services.Analytics.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.Services.Analytics.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.Services.Core.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.Services.Core.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.Services.Core.Editor.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.Services.Core.Editor.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.Services.Core.Internal.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.Services.Core.Internal.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEditor.TestRunner.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEditor.TestRunner.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEditor.UI.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEditor.UI.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEngine.TestRunner.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEngine.TestRunner.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEngine.UI.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEngine.UI.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.203\\RuntimeIdentifierGraph.json"}}}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.Services.Core.Analytics.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.Services.Core.Analytics.csproj", "projectName": "Unity.Services.Core.Analytics", "projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.Services.Core.Analytics.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Temp\\obj\\Debug\\Unity.Services.Core.Analytics\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.Services.Core.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.Services.Core.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEditor.UI.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEditor.UI.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEngine.UI.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEngine.UI.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.203\\RuntimeIdentifierGraph.json"}}}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.Services.Core.Configuration.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.Services.Core.Configuration.csproj", "projectName": "Unity.Services.Core.Configuration", "projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.Services.Core.Configuration.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Temp\\obj\\Debug\\Unity.Services.Core.Configuration\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.Services.Core.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.Services.Core.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.Services.Core.Internal.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.Services.Core.Internal.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEditor.UI.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEditor.UI.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEngine.UI.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEngine.UI.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.203\\RuntimeIdentifierGraph.json"}}}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.Services.Core.Configuration.Editor.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.Services.Core.Configuration.Editor.csproj", "projectName": "Unity.Services.Core.Configuration.Editor", "projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.Services.Core.Configuration.Editor.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Temp\\obj\\Debug\\Unity.Services.Core.Configuration.Editor\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.Services.Core.Configuration.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.Services.Core.Configuration.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.Services.Core.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.Services.Core.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.Services.Core.Internal.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.Services.Core.Internal.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEditor.TestRunner.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEditor.TestRunner.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEditor.UI.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEditor.UI.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEngine.TestRunner.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEngine.TestRunner.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEngine.UI.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEngine.UI.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.203\\RuntimeIdentifierGraph.json"}}}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.Services.Core.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.Services.Core.csproj", "projectName": "Unity.Services.Core", "projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.Services.Core.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Temp\\obj\\Debug\\Unity.Services.Core\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEditor.UI.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEditor.UI.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEngine.UI.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEngine.UI.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.203\\RuntimeIdentifierGraph.json"}}}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.Services.Core.Device.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.Services.Core.Device.csproj", "projectName": "Unity.Services.Core.Device", "projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.Services.Core.Device.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Temp\\obj\\Debug\\Unity.Services.Core.Device\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.Services.Core.Internal.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.Services.Core.Internal.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEditor.UI.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEditor.UI.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEngine.UI.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEngine.UI.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.203\\RuntimeIdentifierGraph.json"}}}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.Services.Core.Editor.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.Services.Core.Editor.csproj", "projectName": "Unity.Services.Core.Editor", "projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.Services.Core.Editor.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Temp\\obj\\Debug\\Unity.Services.Core.Editor\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.Services.Core.Configuration.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.Services.Core.Configuration.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.Services.Core.Configuration.Editor.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.Services.Core.Configuration.Editor.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.Services.Core.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.Services.Core.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.Services.Core.Environments.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.Services.Core.Environments.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.Services.Core.Internal.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.Services.Core.Internal.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.Services.Core.Networking.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.Services.Core.Networking.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.Services.Core.Registration.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.Services.Core.Registration.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.Services.Core.Scheduler.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.Services.Core.Scheduler.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.Services.Core.Telemetry.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.Services.Core.Telemetry.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEditor.TestRunner.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEditor.TestRunner.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEditor.UI.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEditor.UI.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEngine.TestRunner.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEngine.TestRunner.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEngine.UI.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEngine.UI.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.203\\RuntimeIdentifierGraph.json"}}}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.Services.Core.Environments.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.Services.Core.Environments.csproj", "projectName": "Unity.Services.Core.Environments", "projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.Services.Core.Environments.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Temp\\obj\\Debug\\Unity.Services.Core.Environments\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.Services.Core.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.Services.Core.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.Services.Core.Internal.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.Services.Core.Internal.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEditor.UI.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEditor.UI.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEngine.UI.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEngine.UI.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.203\\RuntimeIdentifierGraph.json"}}}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.Services.Core.Environments.Internal.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.Services.Core.Environments.Internal.csproj", "projectName": "Unity.Services.Core.Environments.Internal", "projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.Services.Core.Environments.Internal.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Temp\\obj\\Debug\\Unity.Services.Core.Environments.Internal\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.Services.Core.Internal.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.Services.Core.Internal.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEditor.UI.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEditor.UI.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEngine.UI.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEngine.UI.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.203\\RuntimeIdentifierGraph.json"}}}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.Services.Core.Internal.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.Services.Core.Internal.csproj", "projectName": "Unity.Services.Core.Internal", "projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.Services.Core.Internal.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Temp\\obj\\Debug\\Unity.Services.Core.Internal\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.Services.Core.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.Services.Core.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEditor.UI.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEditor.UI.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEngine.UI.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEngine.UI.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.203\\RuntimeIdentifierGraph.json"}}}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.Services.Core.Networking.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.Services.Core.Networking.csproj", "projectName": "Unity.Services.Core.Networking", "projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.Services.Core.Networking.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Temp\\obj\\Debug\\Unity.Services.Core.Networking\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.Services.Core.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.Services.Core.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.Services.Core.Internal.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.Services.Core.Internal.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEditor.UI.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEditor.UI.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEngine.UI.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEngine.UI.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.203\\RuntimeIdentifierGraph.json"}}}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.Services.Core.Registration.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.Services.Core.Registration.csproj", "projectName": "Unity.Services.Core.Registration", "projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.Services.Core.Registration.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Temp\\obj\\Debug\\Unity.Services.Core.Registration\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.Services.Core.Configuration.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.Services.Core.Configuration.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.Services.Core.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.Services.Core.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.Services.Core.Device.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.Services.Core.Device.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.Services.Core.Environments.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.Services.Core.Environments.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.Services.Core.Environments.Internal.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.Services.Core.Environments.Internal.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.Services.Core.Internal.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.Services.Core.Internal.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.Services.Core.Scheduler.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.Services.Core.Scheduler.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.Services.Core.Telemetry.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.Services.Core.Telemetry.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.Services.Core.Threading.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.Services.Core.Threading.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEditor.UI.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEditor.UI.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEngine.UI.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEngine.UI.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.203\\RuntimeIdentifierGraph.json"}}}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.Services.Core.Scheduler.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.Services.Core.Scheduler.csproj", "projectName": "Unity.Services.Core.Scheduler", "projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.Services.Core.Scheduler.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Temp\\obj\\Debug\\Unity.Services.Core.Scheduler\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.Services.Core.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.Services.Core.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.Services.Core.Internal.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.Services.Core.Internal.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEditor.UI.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEditor.UI.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEngine.UI.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEngine.UI.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.203\\RuntimeIdentifierGraph.json"}}}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.Services.Core.Telemetry.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.Services.Core.Telemetry.csproj", "projectName": "Unity.Services.Core.Telemetry", "projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.Services.Core.Telemetry.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Temp\\obj\\Debug\\Unity.Services.Core.Telemetry\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.Services.Core.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.Services.Core.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.Services.Core.Internal.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.Services.Core.Internal.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEditor.UI.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEditor.UI.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEngine.UI.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEngine.UI.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.203\\RuntimeIdentifierGraph.json"}}}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.Services.Core.Threading.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.Services.Core.Threading.csproj", "projectName": "Unity.Services.Core.Threading", "projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.Services.Core.Threading.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Temp\\obj\\Debug\\Unity.Services.Core.Threading\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.Services.Core.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.Services.Core.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.Services.Core.Internal.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.Services.Core.Internal.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEditor.UI.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEditor.UI.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEngine.UI.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEngine.UI.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.203\\RuntimeIdentifierGraph.json"}}}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.ShaderGraph.Editor.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.ShaderGraph.Editor.csproj", "projectName": "Unity.ShaderGraph.Editor", "projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.ShaderGraph.Editor.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Temp\\obj\\Debug\\Unity.ShaderGraph.Editor\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.RenderPipelines.Core.Editor.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.RenderPipelines.Core.Editor.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.RenderPipelines.Core.Runtime.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.RenderPipelines.Core.Runtime.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.Searcher.Editor.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.Searcher.Editor.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.ShaderGraph.Utilities.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.ShaderGraph.Utilities.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEditor.TestRunner.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEditor.TestRunner.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEditor.UI.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEditor.UI.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEngine.TestRunner.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEngine.TestRunner.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEngine.UI.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEngine.UI.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.203\\RuntimeIdentifierGraph.json"}}}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.ShaderGraph.Utilities.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.ShaderGraph.Utilities.csproj", "projectName": "Unity.ShaderGraph.Utilities", "projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.ShaderGraph.Utilities.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Temp\\obj\\Debug\\Unity.ShaderGraph.Utilities\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEditor.TestRunner.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEditor.TestRunner.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEditor.UI.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEditor.UI.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEngine.TestRunner.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEngine.TestRunner.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEngine.UI.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEngine.UI.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.203\\RuntimeIdentifierGraph.json"}}}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.TextMeshPro.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.TextMeshPro.csproj", "projectName": "Unity.TextMeshPro", "projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.TextMeshPro.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Temp\\obj\\Debug\\Unity.TextMeshPro\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEditor.UI.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEditor.UI.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEngine.UI.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEngine.UI.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.203\\RuntimeIdentifierGraph.json"}}}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.TextMeshPro.Editor.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.TextMeshPro.Editor.csproj", "projectName": "Unity.TextMeshPro.Editor", "projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.TextMeshPro.Editor.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Temp\\obj\\Debug\\Unity.TextMeshPro.Editor\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.TextMeshPro.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.TextMeshPro.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEditor.TestRunner.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEditor.TestRunner.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEditor.UI.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEditor.UI.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEngine.TestRunner.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEngine.TestRunner.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEngine.UI.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEngine.UI.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.203\\RuntimeIdentifierGraph.json"}}}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.Timeline.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.Timeline.csproj", "projectName": "Unity.Timeline", "projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.Timeline.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Temp\\obj\\Debug\\Unity.Timeline\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEditor.UI.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEditor.UI.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEngine.UI.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEngine.UI.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.203\\RuntimeIdentifierGraph.json"}}}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.Timeline.Editor.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.Timeline.Editor.csproj", "projectName": "Unity.Timeline.Editor", "projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.Timeline.Editor.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Temp\\obj\\Debug\\Unity.Timeline.Editor\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.Timeline.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.Timeline.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEditor.TestRunner.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEditor.TestRunner.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEditor.UI.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEditor.UI.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEngine.TestRunner.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEngine.TestRunner.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEngine.UI.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEngine.UI.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.203\\RuntimeIdentifierGraph.json"}}}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.VisualStudio.Editor.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.VisualStudio.Editor.csproj", "projectName": "Unity.VisualStudio.Editor", "projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.VisualStudio.Editor.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Temp\\obj\\Debug\\Unity.VisualStudio.Editor\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEditor.TestRunner.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEditor.TestRunner.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEditor.UI.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEditor.UI.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEngine.TestRunner.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEngine.TestRunner.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEngine.UI.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEngine.UI.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.203\\RuntimeIdentifierGraph.json"}}}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.VSCode.Editor.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.VSCode.Editor.csproj", "projectName": "Unity.VSCode.Editor", "projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.VSCode.Editor.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Temp\\obj\\Debug\\Unity.VSCode.Editor\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEditor.TestRunner.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEditor.TestRunner.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEditor.UI.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEditor.UI.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEngine.TestRunner.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEngine.TestRunner.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEngine.UI.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEngine.UI.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.203\\RuntimeIdentifierGraph.json"}}}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEditor.SpatialTracking.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEditor.SpatialTracking.csproj", "projectName": "UnityEditor.SpatialTracking", "projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEditor.SpatialTracking.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Temp\\obj\\Debug\\UnityEditor.SpatialTracking\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEditor.TestRunner.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEditor.TestRunner.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEditor.UI.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEditor.UI.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEngine.SpatialTracking.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEngine.SpatialTracking.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEngine.TestRunner.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEngine.TestRunner.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEngine.UI.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEngine.UI.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.203\\RuntimeIdentifierGraph.json"}}}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEditor.TestRunner.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEditor.TestRunner.csproj", "projectName": "UnityEditor.TestRunner", "projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEditor.TestRunner.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Temp\\obj\\Debug\\UnityEditor.TestRunner\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEngine.TestRunner.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEngine.TestRunner.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.203\\RuntimeIdentifierGraph.json"}}}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEditor.UI.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEditor.UI.csproj", "projectName": "UnityEditor.UI", "projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEditor.UI.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Temp\\obj\\Debug\\UnityEditor.UI\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEditor.TestRunner.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEditor.TestRunner.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEngine.TestRunner.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEngine.TestRunner.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEngine.UI.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEngine.UI.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.203\\RuntimeIdentifierGraph.json"}}}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEditor.XR.LegacyInputHelpers.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEditor.XR.LegacyInputHelpers.csproj", "projectName": "UnityEditor.XR.LegacyInputHelpers", "projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEditor.XR.LegacyInputHelpers.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Temp\\obj\\Debug\\UnityEditor.XR.LegacyInputHelpers\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEditor.TestRunner.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEditor.TestRunner.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEditor.UI.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEditor.UI.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEngine.SpatialTracking.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEngine.SpatialTracking.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEngine.TestRunner.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEngine.TestRunner.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEngine.UI.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEngine.UI.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEngine.XR.LegacyInputHelpers.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEngine.XR.LegacyInputHelpers.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.203\\RuntimeIdentifierGraph.json"}}}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEngine.Advertisements.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEngine.Advertisements.csproj", "projectName": "UnityEngine.Advertisements", "projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEngine.Advertisements.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Temp\\obj\\Debug\\UnityEngine.Advertisements\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEditor.UI.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEditor.UI.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEngine.UI.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEngine.UI.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.203\\RuntimeIdentifierGraph.json"}}}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEngine.Advertisements.Editor.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEngine.Advertisements.Editor.csproj", "projectName": "UnityEngine.Advertisements.Editor", "projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEngine.Advertisements.Editor.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Temp\\obj\\Debug\\UnityEngine.Advertisements.Editor\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEditor.TestRunner.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEditor.TestRunner.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEditor.UI.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEditor.UI.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEngine.TestRunner.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEngine.TestRunner.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEngine.UI.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEngine.UI.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.203\\RuntimeIdentifierGraph.json"}}}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEngine.SpatialTracking.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEngine.SpatialTracking.csproj", "projectName": "UnityEngine.SpatialTracking", "projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEngine.SpatialTracking.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Temp\\obj\\Debug\\UnityEngine.SpatialTracking\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEditor.UI.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEditor.UI.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEngine.UI.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEngine.UI.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.203\\RuntimeIdentifierGraph.json"}}}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEngine.TestRunner.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEngine.TestRunner.csproj", "projectName": "UnityEngine.TestRunner", "projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEngine.TestRunner.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Temp\\obj\\Debug\\UnityEngine.TestRunner\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.203\\RuntimeIdentifierGraph.json"}}}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEngine.UI.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEngine.UI.csproj", "projectName": "UnityEngine.UI", "projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEngine.UI.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Temp\\obj\\Debug\\UnityEngine.UI\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.203\\RuntimeIdentifierGraph.json"}}}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEngine.XR.LegacyInputHelpers.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEngine.XR.LegacyInputHelpers.csproj", "projectName": "UnityEngine.XR.LegacyInputHelpers", "projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEngine.XR.LegacyInputHelpers.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Temp\\obj\\Debug\\UnityEngine.XR.LegacyInputHelpers\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEditor.UI.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEditor.UI.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEngine.SpatialTracking.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEngine.SpatialTracking.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEngine.UI.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEngine.UI.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.203\\RuntimeIdentifierGraph.json"}}}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityGLTF.Helpers.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityGLTF.Helpers.csproj", "projectName": "UnityGLTF.Helpers", "projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityGLTF.Helpers.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Temp\\obj\\Debug\\UnityGLTF.Helpers\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEditor.UI.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEditor.UI.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEngine.UI.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEngine.UI.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.203\\RuntimeIdentifierGraph.json"}}}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityGLTF.Interactivity.Runtime.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityGLTF.Interactivity.Runtime.csproj", "projectName": "UnityGLTF.Interactivity.Runtime", "projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityGLTF.Interactivity.Runtime.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Temp\\obj\\Debug\\UnityGLTF.Interactivity.Runtime\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\GLTFSerialization.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\GLTFSerialization.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEditor.UI.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEditor.UI.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEngine.UI.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEngine.UI.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityGLTFScripts.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityGLTFScripts.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.203\\RuntimeIdentifierGraph.json"}}}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityGLTF.Interactivity.VisualScriptingInstall.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityGLTF.Interactivity.VisualScriptingInstall.csproj", "projectName": "UnityGLTF.Interactivity.VisualScriptingInstall", "projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityGLTF.Interactivity.VisualScriptingInstall.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Temp\\obj\\Debug\\UnityGLTF.Interactivity.VisualScriptingInstall\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEditor.TestRunner.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEditor.TestRunner.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEditor.UI.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEditor.UI.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEngine.TestRunner.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEngine.TestRunner.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEngine.UI.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEngine.UI.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityGLTFEditor.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityGLTFEditor.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityGLTFScripts.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityGLTFScripts.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.203\\RuntimeIdentifierGraph.json"}}}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityGLTFEditor.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityGLTFEditor.csproj", "projectName": "UnityGLTFEditor", "projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityGLTFEditor.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Temp\\obj\\Debug\\UnityGLTFEditor\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\GLTFSerialization.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\GLTFSerialization.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEditor.TestRunner.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEditor.TestRunner.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEditor.UI.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEditor.UI.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEngine.TestRunner.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEngine.TestRunner.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEngine.UI.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEngine.UI.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityGLTFScripts.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityGLTFScripts.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.203\\RuntimeIdentifierGraph.json"}}}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityGLTFScripts.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityGLTFScripts.csproj", "projectName": "UnityGLTFScripts", "projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityGLTFScripts.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Temp\\obj\\Debug\\UnityGLTFScripts\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\GLTFSerialization.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\GLTFSerialization.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.Mathematics.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.Mathematics.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.Timeline.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.Timeline.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEditor.UI.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEditor.UI.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEngine.UI.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEngine.UI.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityGLTF.Helpers.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityGLTF.Helpers.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.203\\RuntimeIdentifierGraph.json"}}}}}