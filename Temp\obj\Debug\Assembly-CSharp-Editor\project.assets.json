{"version": 3, "targets": {".NETStandard,Version=v2.1": {"Analytics/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Analytics.dll": {}}, "runtime": {"bin/placeholder/Analytics.dll": {}}}, "Assembly-CSharp/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Analytics": "1.0.0", "Autodesk.Fbx": "1.0.0", "Autodesk.Fbx.Editor": "1.0.0", "GLTFSerialization": "1.0.0", "McpUnity.Editor": "1.0.0", "Unity.2D.Sprite.Editor": "1.0.0", "Unity.2D.Tilemap.Editor": "1.0.0", "Unity.AI.Navigation": "1.0.0", "Unity.AI.Navigation.Editor": "1.0.0", "Unity.AI.Navigation.Editor.ConversionSystem": "1.0.0", "Unity.AI.Navigation.Updater": "1.0.0", "Unity.Analytics.DataPrivacy": "1.0.0", "Unity.EditorCoroutines.Editor": "1.0.0", "Unity.Formats.Fbx.Editor": "1.0.0", "Unity.Formats.Fbx.Runtime": "1.0.0", "Unity.Mathematics": "1.0.0", "Unity.Mathematics.Editor": "1.0.0", "Unity.PlasticSCM.Editor": "1.0.0", "Unity.RenderPipelines.Core.Editor": "1.0.0", "Unity.RenderPipelines.Core.Runtime": "1.0.0", "Unity.RenderPipelines.Core.ShaderLibrary": "1.0.0", "Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary": "1.0.0", "Unity.Rider.Editor": "1.0.0", "Unity.Searcher.Editor": "1.0.0", "Unity.Services.Analytics": "1.0.0", "Unity.Services.Analytics.Editor": "1.0.0", "Unity.Services.Core": "1.0.0", "Unity.Services.Core.Analytics": "1.0.0", "Unity.Services.Core.Environments": "1.0.0", "Unity.ShaderGraph.Editor": "1.0.0", "Unity.TextMeshPro": "1.0.0", "Unity.TextMeshPro.Editor": "1.0.0", "Unity.Timeline": "1.0.0", "Unity.Timeline.Editor": "1.0.0", "Unity.VSCode.Editor": "1.0.0", "Unity.VisualStudio.Editor": "1.0.0", "UnityEditor.SpatialTracking": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEditor.XR.LegacyInputHelpers": "1.0.0", "UnityEngine.Advertisements": "1.0.0", "UnityEngine.Advertisements.Editor": "1.0.0", "UnityEngine.SpatialTracking": "1.0.0", "UnityEngine.UI": "1.0.0", "UnityEngine.XR.LegacyInputHelpers": "1.0.0", "UnityGLTF.Interactivity.Runtime": "1.0.0", "UnityGLTF.Interactivity.VisualScriptingInstall": "1.0.0", "UnityGLTFEditor": "1.0.0", "UnityGLTFScripts": "1.0.0"}, "compile": {"bin/placeholder/Assembly-CSharp.dll": {}}, "runtime": {"bin/placeholder/Assembly-CSharp.dll": {}}}, "Autodesk.Fbx/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Autodesk.Fbx.dll": {}}, "runtime": {"bin/placeholder/Autodesk.Fbx.dll": {}}}, "Autodesk.Fbx.Editor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Autodesk.Fbx.Editor.dll": {}}, "runtime": {"bin/placeholder/Autodesk.Fbx.Editor.dll": {}}}, "GLTFSerialization/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.Mathematics": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/GLTFSerialization.dll": {}}, "runtime": {"bin/placeholder/GLTFSerialization.dll": {}}}, "McpUnity.Editor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.EditorCoroutines.Editor": "1.0.0", "UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/McpUnity.Editor.dll": {}}, "runtime": {"bin/placeholder/McpUnity.Editor.dll": {}}}, "Unity.2D.Sprite.Editor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.2D.Sprite.Editor.dll": {}}, "runtime": {"bin/placeholder/Unity.2D.Sprite.Editor.dll": {}}}, "Unity.2D.Tilemap.Editor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.2D.Tilemap.Editor.dll": {}}, "runtime": {"bin/placeholder/Unity.2D.Tilemap.Editor.dll": {}}}, "Unity.AI.Navigation/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.AI.Navigation.dll": {}}, "runtime": {"bin/placeholder/Unity.AI.Navigation.dll": {}}}, "Unity.AI.Navigation.Editor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.AI.Navigation": "1.0.0", "Unity.AI.Navigation.Editor.ConversionSystem": "1.0.0", "UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.AI.Navigation.Editor.dll": {}}, "runtime": {"bin/placeholder/Unity.AI.Navigation.Editor.dll": {}}}, "Unity.AI.Navigation.Editor.ConversionSystem/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.AI.Navigation.Editor.ConversionSystem.dll": {}}, "runtime": {"bin/placeholder/Unity.AI.Navigation.Editor.ConversionSystem.dll": {}}}, "Unity.AI.Navigation.Updater/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.AI.Navigation": "1.0.0", "Unity.AI.Navigation.Editor": "1.0.0", "Unity.AI.Navigation.Editor.ConversionSystem": "1.0.0", "UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.AI.Navigation.Updater.dll": {}}, "runtime": {"bin/placeholder/Unity.AI.Navigation.Updater.dll": {}}}, "Unity.Analytics.DataPrivacy/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.Analytics.DataPrivacy.dll": {}}, "runtime": {"bin/placeholder/Unity.Analytics.DataPrivacy.dll": {}}}, "Unity.EditorCoroutines.Editor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.EditorCoroutines.Editor.dll": {}}, "runtime": {"bin/placeholder/Unity.EditorCoroutines.Editor.dll": {}}}, "Unity.Formats.Fbx.Editor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Autodesk.Fbx": "1.0.0", "Unity.Formats.Fbx.Runtime": "1.0.0", "Unity.Timeline": "1.0.0", "Unity.Timeline.Editor": "1.0.0", "UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.Formats.Fbx.Editor.dll": {}}, "runtime": {"bin/placeholder/Unity.Formats.Fbx.Editor.dll": {}}}, "Unity.Formats.Fbx.Runtime/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.Formats.Fbx.Runtime.dll": {}}, "runtime": {"bin/placeholder/Unity.Formats.Fbx.Runtime.dll": {}}}, "Unity.Mathematics/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.Mathematics.dll": {}}, "runtime": {"bin/placeholder/Unity.Mathematics.dll": {}}}, "Unity.Mathematics.Editor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.Mathematics": "1.0.0", "UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.Mathematics.Editor.dll": {}}, "runtime": {"bin/placeholder/Unity.Mathematics.Editor.dll": {}}}, "Unity.PlasticSCM.Editor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.PlasticSCM.Editor.dll": {}}, "runtime": {"bin/placeholder/Unity.PlasticSCM.Editor.dll": {}}}, "Unity.RenderPipelines.Core.Editor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.RenderPipelines.Core.Runtime": "1.0.0", "UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.RenderPipelines.Core.Editor.dll": {}}, "runtime": {"bin/placeholder/Unity.RenderPipelines.Core.Editor.dll": {}}}, "Unity.RenderPipelines.Core.Runtime/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.RenderPipelines.Core.Runtime.dll": {}}, "runtime": {"bin/placeholder/Unity.RenderPipelines.Core.Runtime.dll": {}}}, "Unity.RenderPipelines.Core.ShaderLibrary/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.RenderPipelines.Core.ShaderLibrary.dll": {}}, "runtime": {"bin/placeholder/Unity.RenderPipelines.Core.ShaderLibrary.dll": {}}}, "Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll": {}}, "runtime": {"bin/placeholder/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll": {}}}, "Unity.Rider.Editor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.Rider.Editor.dll": {}}, "runtime": {"bin/placeholder/Unity.Rider.Editor.dll": {}}}, "Unity.Searcher.Editor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.Searcher.Editor.dll": {}}, "runtime": {"bin/placeholder/Unity.Searcher.Editor.dll": {}}}, "Unity.Services.Analytics/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.Services.Core": "1.0.0", "Unity.Services.Core.Internal": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.Services.Analytics.dll": {}}, "runtime": {"bin/placeholder/Unity.Services.Analytics.dll": {}}}, "Unity.Services.Analytics.Editor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.Services.Analytics": "1.0.0", "Unity.Services.Core": "1.0.0", "Unity.Services.Core.Editor": "1.0.0", "Unity.Services.Core.Internal": "1.0.0", "UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.Services.Analytics.Editor.dll": {}}, "runtime": {"bin/placeholder/Unity.Services.Analytics.Editor.dll": {}}}, "Unity.Services.Core/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.Services.Core.dll": {}}, "runtime": {"bin/placeholder/Unity.Services.Core.dll": {}}}, "Unity.Services.Core.Analytics/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.Services.Core": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.Services.Core.Analytics.dll": {}}, "runtime": {"bin/placeholder/Unity.Services.Core.Analytics.dll": {}}}, "Unity.Services.Core.Configuration/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.Services.Core": "1.0.0", "Unity.Services.Core.Internal": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.Services.Core.Configuration.dll": {}}, "runtime": {"bin/placeholder/Unity.Services.Core.Configuration.dll": {}}}, "Unity.Services.Core.Configuration.Editor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.Services.Core": "1.0.0", "Unity.Services.Core.Configuration": "1.0.0", "Unity.Services.Core.Internal": "1.0.0", "UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.Services.Core.Configuration.Editor.dll": {}}, "runtime": {"bin/placeholder/Unity.Services.Core.Configuration.Editor.dll": {}}}, "Unity.Services.Core.Device/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.Services.Core.Internal": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.Services.Core.Device.dll": {}}, "runtime": {"bin/placeholder/Unity.Services.Core.Device.dll": {}}}, "Unity.Services.Core.Editor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.Services.Core": "1.0.0", "Unity.Services.Core.Configuration": "1.0.0", "Unity.Services.Core.Configuration.Editor": "1.0.0", "Unity.Services.Core.Environments": "1.0.0", "Unity.Services.Core.Internal": "1.0.0", "Unity.Services.Core.Networking": "1.0.0", "Unity.Services.Core.Registration": "1.0.0", "Unity.Services.Core.Scheduler": "1.0.0", "Unity.Services.Core.Telemetry": "1.0.0", "UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.Services.Core.Editor.dll": {}}, "runtime": {"bin/placeholder/Unity.Services.Core.Editor.dll": {}}}, "Unity.Services.Core.Environments/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.Services.Core": "1.0.0", "Unity.Services.Core.Internal": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.Services.Core.Environments.dll": {}}, "runtime": {"bin/placeholder/Unity.Services.Core.Environments.dll": {}}}, "Unity.Services.Core.Environments.Internal/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.Services.Core.Internal": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.Services.Core.Environments.Internal.dll": {}}, "runtime": {"bin/placeholder/Unity.Services.Core.Environments.Internal.dll": {}}}, "Unity.Services.Core.Internal/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.Services.Core": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.Services.Core.Internal.dll": {}}, "runtime": {"bin/placeholder/Unity.Services.Core.Internal.dll": {}}}, "Unity.Services.Core.Networking/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.Services.Core": "1.0.0", "Unity.Services.Core.Internal": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.Services.Core.Networking.dll": {}}, "runtime": {"bin/placeholder/Unity.Services.Core.Networking.dll": {}}}, "Unity.Services.Core.Registration/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.Services.Core": "1.0.0", "Unity.Services.Core.Configuration": "1.0.0", "Unity.Services.Core.Device": "1.0.0", "Unity.Services.Core.Environments": "1.0.0", "Unity.Services.Core.Environments.Internal": "1.0.0", "Unity.Services.Core.Internal": "1.0.0", "Unity.Services.Core.Scheduler": "1.0.0", "Unity.Services.Core.Telemetry": "1.0.0", "Unity.Services.Core.Threading": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.Services.Core.Registration.dll": {}}, "runtime": {"bin/placeholder/Unity.Services.Core.Registration.dll": {}}}, "Unity.Services.Core.Scheduler/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.Services.Core": "1.0.0", "Unity.Services.Core.Internal": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.Services.Core.Scheduler.dll": {}}, "runtime": {"bin/placeholder/Unity.Services.Core.Scheduler.dll": {}}}, "Unity.Services.Core.Telemetry/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.Services.Core": "1.0.0", "Unity.Services.Core.Internal": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.Services.Core.Telemetry.dll": {}}, "runtime": {"bin/placeholder/Unity.Services.Core.Telemetry.dll": {}}}, "Unity.Services.Core.Threading/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.Services.Core": "1.0.0", "Unity.Services.Core.Internal": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.Services.Core.Threading.dll": {}}, "runtime": {"bin/placeholder/Unity.Services.Core.Threading.dll": {}}}, "Unity.ShaderGraph.Editor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.RenderPipelines.Core.Editor": "1.0.0", "Unity.RenderPipelines.Core.Runtime": "1.0.0", "Unity.Searcher.Editor": "1.0.0", "Unity.ShaderGraph.Utilities": "1.0.0", "UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.ShaderGraph.Editor.dll": {}}, "runtime": {"bin/placeholder/Unity.ShaderGraph.Editor.dll": {}}}, "Unity.ShaderGraph.Utilities/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.ShaderGraph.Utilities.dll": {}}, "runtime": {"bin/placeholder/Unity.ShaderGraph.Utilities.dll": {}}}, "Unity.TextMeshPro/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.TextMeshPro.dll": {}}, "runtime": {"bin/placeholder/Unity.TextMeshPro.dll": {}}}, "Unity.TextMeshPro.Editor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.TextMeshPro": "1.0.0", "UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.TextMeshPro.Editor.dll": {}}, "runtime": {"bin/placeholder/Unity.TextMeshPro.Editor.dll": {}}}, "Unity.Timeline/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.Timeline.dll": {}}, "runtime": {"bin/placeholder/Unity.Timeline.dll": {}}}, "Unity.Timeline.Editor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.Timeline": "1.0.0", "UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.Timeline.Editor.dll": {}}, "runtime": {"bin/placeholder/Unity.Timeline.Editor.dll": {}}}, "Unity.VisualStudio.Editor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.VisualStudio.Editor.dll": {}}, "runtime": {"bin/placeholder/Unity.VisualStudio.Editor.dll": {}}}, "Unity.VSCode.Editor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.VSCode.Editor.dll": {}}, "runtime": {"bin/placeholder/Unity.VSCode.Editor.dll": {}}}, "UnityEditor.SpatialTracking/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.SpatialTracking": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/UnityEditor.SpatialTracking.dll": {}}, "runtime": {"bin/placeholder/UnityEditor.SpatialTracking.dll": {}}}, "UnityEditor.TestRunner/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"UnityEngine.TestRunner": "1.0.0"}, "compile": {"bin/placeholder/UnityEditor.TestRunner.dll": {}}, "runtime": {"bin/placeholder/UnityEditor.TestRunner.dll": {}}}, "UnityEditor.UI/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"UnityEditor.TestRunner": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/UnityEditor.UI.dll": {}}, "runtime": {"bin/placeholder/UnityEditor.UI.dll": {}}}, "UnityEditor.XR.LegacyInputHelpers/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.SpatialTracking": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0", "UnityEngine.XR.LegacyInputHelpers": "1.0.0"}, "compile": {"bin/placeholder/UnityEditor.XR.LegacyInputHelpers.dll": {}}, "runtime": {"bin/placeholder/UnityEditor.XR.LegacyInputHelpers.dll": {}}}, "UnityEngine.Advertisements/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/UnityEngine.Advertisements.dll": {}}, "runtime": {"bin/placeholder/UnityEngine.Advertisements.dll": {}}}, "UnityEngine.Advertisements.Editor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/UnityEngine.Advertisements.Editor.dll": {}}, "runtime": {"bin/placeholder/UnityEngine.Advertisements.Editor.dll": {}}}, "UnityEngine.SpatialTracking/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/UnityEngine.SpatialTracking.dll": {}}, "runtime": {"bin/placeholder/UnityEngine.SpatialTracking.dll": {}}}, "UnityEngine.TestRunner/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "compile": {"bin/placeholder/UnityEngine.TestRunner.dll": {}}, "runtime": {"bin/placeholder/UnityEngine.TestRunner.dll": {}}}, "UnityEngine.UI/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "compile": {"bin/placeholder/UnityEngine.UI.dll": {}}, "runtime": {"bin/placeholder/UnityEngine.UI.dll": {}}}, "UnityEngine.XR.LegacyInputHelpers/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"UnityEditor.UI": "1.0.0", "UnityEngine.SpatialTracking": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/UnityEngine.XR.LegacyInputHelpers.dll": {}}, "runtime": {"bin/placeholder/UnityEngine.XR.LegacyInputHelpers.dll": {}}}, "UnityGLTF.Helpers/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/UnityGLTF.Helpers.dll": {}}, "runtime": {"bin/placeholder/UnityGLTF.Helpers.dll": {}}}, "UnityGLTF.Interactivity.Runtime/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"GLTFSerialization": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0", "UnityGLTFScripts": "1.0.0"}, "compile": {"bin/placeholder/UnityGLTF.Interactivity.Runtime.dll": {}}, "runtime": {"bin/placeholder/UnityGLTF.Interactivity.Runtime.dll": {}}}, "UnityGLTF.Interactivity.VisualScriptingInstall/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0", "UnityGLTFEditor": "1.0.0", "UnityGLTFScripts": "1.0.0"}, "compile": {"bin/placeholder/UnityGLTF.Interactivity.VisualScriptingInstall.dll": {}}, "runtime": {"bin/placeholder/UnityGLTF.Interactivity.VisualScriptingInstall.dll": {}}}, "UnityGLTFEditor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"GLTFSerialization": "1.0.0", "UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0", "UnityGLTFScripts": "1.0.0"}, "compile": {"bin/placeholder/UnityGLTFEditor.dll": {}}, "runtime": {"bin/placeholder/UnityGLTFEditor.dll": {}}}, "UnityGLTFScripts/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"GLTFSerialization": "1.0.0", "Unity.Mathematics": "1.0.0", "Unity.Timeline": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0", "UnityGLTF.Helpers": "1.0.0"}, "compile": {"bin/placeholder/UnityGLTFScripts.dll": {}}, "runtime": {"bin/placeholder/UnityGLTFScripts.dll": {}}}}}, "libraries": {"Analytics/1.0.0": {"type": "project", "path": "Analytics.csproj", "msbuildProject": "Analytics.csproj"}, "Assembly-CSharp/1.0.0": {"type": "project", "path": "Assembly-CSharp.csproj", "msbuildProject": "Assembly-CSharp.csproj"}, "Autodesk.Fbx/1.0.0": {"type": "project", "path": "Autodesk.Fbx.csproj", "msbuildProject": "Autodesk.Fbx.csproj"}, "Autodesk.Fbx.Editor/1.0.0": {"type": "project", "path": "Autodesk.Fbx.Editor.csproj", "msbuildProject": "Autodesk.Fbx.Editor.csproj"}, "GLTFSerialization/1.0.0": {"type": "project", "path": "GLTFSerialization.csproj", "msbuildProject": "GLTFSerialization.csproj"}, "McpUnity.Editor/1.0.0": {"type": "project", "path": "McpUnity.Editor.csproj", "msbuildProject": "McpUnity.Editor.csproj"}, "Unity.2D.Sprite.Editor/1.0.0": {"type": "project", "path": "Unity.2D.Sprite.Editor.csproj", "msbuildProject": "Unity.2D.Sprite.Editor.csproj"}, "Unity.2D.Tilemap.Editor/1.0.0": {"type": "project", "path": "Unity.2D.Tilemap.Editor.csproj", "msbuildProject": "Unity.2D.Tilemap.Editor.csproj"}, "Unity.AI.Navigation/1.0.0": {"type": "project", "path": "Unity.AI.Navigation.csproj", "msbuildProject": "Unity.AI.Navigation.csproj"}, "Unity.AI.Navigation.Editor/1.0.0": {"type": "project", "path": "Unity.AI.Navigation.Editor.csproj", "msbuildProject": "Unity.AI.Navigation.Editor.csproj"}, "Unity.AI.Navigation.Editor.ConversionSystem/1.0.0": {"type": "project", "path": "Unity.AI.Navigation.Editor.ConversionSystem.csproj", "msbuildProject": "Unity.AI.Navigation.Editor.ConversionSystem.csproj"}, "Unity.AI.Navigation.Updater/1.0.0": {"type": "project", "path": "Unity.AI.Navigation.Updater.csproj", "msbuildProject": "Unity.AI.Navigation.Updater.csproj"}, "Unity.Analytics.DataPrivacy/1.0.0": {"type": "project", "path": "Unity.Analytics.DataPrivacy.csproj", "msbuildProject": "Unity.Analytics.DataPrivacy.csproj"}, "Unity.EditorCoroutines.Editor/1.0.0": {"type": "project", "path": "Unity.EditorCoroutines.Editor.csproj", "msbuildProject": "Unity.EditorCoroutines.Editor.csproj"}, "Unity.Formats.Fbx.Editor/1.0.0": {"type": "project", "path": "Unity.Formats.Fbx.Editor.csproj", "msbuildProject": "Unity.Formats.Fbx.Editor.csproj"}, "Unity.Formats.Fbx.Runtime/1.0.0": {"type": "project", "path": "Unity.Formats.Fbx.Runtime.csproj", "msbuildProject": "Unity.Formats.Fbx.Runtime.csproj"}, "Unity.Mathematics/1.0.0": {"type": "project", "path": "Unity.Mathematics.csproj", "msbuildProject": "Unity.Mathematics.csproj"}, "Unity.Mathematics.Editor/1.0.0": {"type": "project", "path": "Unity.Mathematics.Editor.csproj", "msbuildProject": "Unity.Mathematics.Editor.csproj"}, "Unity.PlasticSCM.Editor/1.0.0": {"type": "project", "path": "Unity.PlasticSCM.Editor.csproj", "msbuildProject": "Unity.PlasticSCM.Editor.csproj"}, "Unity.RenderPipelines.Core.Editor/1.0.0": {"type": "project", "path": "Unity.RenderPipelines.Core.Editor.csproj", "msbuildProject": "Unity.RenderPipelines.Core.Editor.csproj"}, "Unity.RenderPipelines.Core.Runtime/1.0.0": {"type": "project", "path": "Unity.RenderPipelines.Core.Runtime.csproj", "msbuildProject": "Unity.RenderPipelines.Core.Runtime.csproj"}, "Unity.RenderPipelines.Core.ShaderLibrary/1.0.0": {"type": "project", "path": "Unity.RenderPipelines.Core.ShaderLibrary.csproj", "msbuildProject": "Unity.RenderPipelines.Core.ShaderLibrary.csproj"}, "Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary/1.0.0": {"type": "project", "path": "Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.csproj", "msbuildProject": "Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.csproj"}, "Unity.Rider.Editor/1.0.0": {"type": "project", "path": "Unity.Rider.Editor.csproj", "msbuildProject": "Unity.Rider.Editor.csproj"}, "Unity.Searcher.Editor/1.0.0": {"type": "project", "path": "Unity.Searcher.Editor.csproj", "msbuildProject": "Unity.Searcher.Editor.csproj"}, "Unity.Services.Analytics/1.0.0": {"type": "project", "path": "Unity.Services.Analytics.csproj", "msbuildProject": "Unity.Services.Analytics.csproj"}, "Unity.Services.Analytics.Editor/1.0.0": {"type": "project", "path": "Unity.Services.Analytics.Editor.csproj", "msbuildProject": "Unity.Services.Analytics.Editor.csproj"}, "Unity.Services.Core/1.0.0": {"type": "project", "path": "Unity.Services.Core.csproj", "msbuildProject": "Unity.Services.Core.csproj"}, "Unity.Services.Core.Analytics/1.0.0": {"type": "project", "path": "Unity.Services.Core.Analytics.csproj", "msbuildProject": "Unity.Services.Core.Analytics.csproj"}, "Unity.Services.Core.Configuration/1.0.0": {"type": "project", "path": "Unity.Services.Core.Configuration.csproj", "msbuildProject": "Unity.Services.Core.Configuration.csproj"}, "Unity.Services.Core.Configuration.Editor/1.0.0": {"type": "project", "path": "Unity.Services.Core.Configuration.Editor.csproj", "msbuildProject": "Unity.Services.Core.Configuration.Editor.csproj"}, "Unity.Services.Core.Device/1.0.0": {"type": "project", "path": "Unity.Services.Core.Device.csproj", "msbuildProject": "Unity.Services.Core.Device.csproj"}, "Unity.Services.Core.Editor/1.0.0": {"type": "project", "path": "Unity.Services.Core.Editor.csproj", "msbuildProject": "Unity.Services.Core.Editor.csproj"}, "Unity.Services.Core.Environments/1.0.0": {"type": "project", "path": "Unity.Services.Core.Environments.csproj", "msbuildProject": "Unity.Services.Core.Environments.csproj"}, "Unity.Services.Core.Environments.Internal/1.0.0": {"type": "project", "path": "Unity.Services.Core.Environments.Internal.csproj", "msbuildProject": "Unity.Services.Core.Environments.Internal.csproj"}, "Unity.Services.Core.Internal/1.0.0": {"type": "project", "path": "Unity.Services.Core.Internal.csproj", "msbuildProject": "Unity.Services.Core.Internal.csproj"}, "Unity.Services.Core.Networking/1.0.0": {"type": "project", "path": "Unity.Services.Core.Networking.csproj", "msbuildProject": "Unity.Services.Core.Networking.csproj"}, "Unity.Services.Core.Registration/1.0.0": {"type": "project", "path": "Unity.Services.Core.Registration.csproj", "msbuildProject": "Unity.Services.Core.Registration.csproj"}, "Unity.Services.Core.Scheduler/1.0.0": {"type": "project", "path": "Unity.Services.Core.Scheduler.csproj", "msbuildProject": "Unity.Services.Core.Scheduler.csproj"}, "Unity.Services.Core.Telemetry/1.0.0": {"type": "project", "path": "Unity.Services.Core.Telemetry.csproj", "msbuildProject": "Unity.Services.Core.Telemetry.csproj"}, "Unity.Services.Core.Threading/1.0.0": {"type": "project", "path": "Unity.Services.Core.Threading.csproj", "msbuildProject": "Unity.Services.Core.Threading.csproj"}, "Unity.ShaderGraph.Editor/1.0.0": {"type": "project", "path": "Unity.ShaderGraph.Editor.csproj", "msbuildProject": "Unity.ShaderGraph.Editor.csproj"}, "Unity.ShaderGraph.Utilities/1.0.0": {"type": "project", "path": "Unity.ShaderGraph.Utilities.csproj", "msbuildProject": "Unity.ShaderGraph.Utilities.csproj"}, "Unity.TextMeshPro/1.0.0": {"type": "project", "path": "Unity.TextMeshPro.csproj", "msbuildProject": "Unity.TextMeshPro.csproj"}, "Unity.TextMeshPro.Editor/1.0.0": {"type": "project", "path": "Unity.TextMeshPro.Editor.csproj", "msbuildProject": "Unity.TextMeshPro.Editor.csproj"}, "Unity.Timeline/1.0.0": {"type": "project", "path": "Unity.Timeline.csproj", "msbuildProject": "Unity.Timeline.csproj"}, "Unity.Timeline.Editor/1.0.0": {"type": "project", "path": "Unity.Timeline.Editor.csproj", "msbuildProject": "Unity.Timeline.Editor.csproj"}, "Unity.VisualStudio.Editor/1.0.0": {"type": "project", "path": "Unity.VisualStudio.Editor.csproj", "msbuildProject": "Unity.VisualStudio.Editor.csproj"}, "Unity.VSCode.Editor/1.0.0": {"type": "project", "path": "Unity.VSCode.Editor.csproj", "msbuildProject": "Unity.VSCode.Editor.csproj"}, "UnityEditor.SpatialTracking/1.0.0": {"type": "project", "path": "UnityEditor.SpatialTracking.csproj", "msbuildProject": "UnityEditor.SpatialTracking.csproj"}, "UnityEditor.TestRunner/1.0.0": {"type": "project", "path": "UnityEditor.TestRunner.csproj", "msbuildProject": "UnityEditor.TestRunner.csproj"}, "UnityEditor.UI/1.0.0": {"type": "project", "path": "UnityEditor.UI.csproj", "msbuildProject": "UnityEditor.UI.csproj"}, "UnityEditor.XR.LegacyInputHelpers/1.0.0": {"type": "project", "path": "UnityEditor.XR.LegacyInputHelpers.csproj", "msbuildProject": "UnityEditor.XR.LegacyInputHelpers.csproj"}, "UnityEngine.Advertisements/1.0.0": {"type": "project", "path": "UnityEngine.Advertisements.csproj", "msbuildProject": "UnityEngine.Advertisements.csproj"}, "UnityEngine.Advertisements.Editor/1.0.0": {"type": "project", "path": "UnityEngine.Advertisements.Editor.csproj", "msbuildProject": "UnityEngine.Advertisements.Editor.csproj"}, "UnityEngine.SpatialTracking/1.0.0": {"type": "project", "path": "UnityEngine.SpatialTracking.csproj", "msbuildProject": "UnityEngine.SpatialTracking.csproj"}, "UnityEngine.TestRunner/1.0.0": {"type": "project", "path": "UnityEngine.TestRunner.csproj", "msbuildProject": "UnityEngine.TestRunner.csproj"}, "UnityEngine.UI/1.0.0": {"type": "project", "path": "UnityEngine.UI.csproj", "msbuildProject": "UnityEngine.UI.csproj"}, "UnityEngine.XR.LegacyInputHelpers/1.0.0": {"type": "project", "path": "UnityEngine.XR.LegacyInputHelpers.csproj", "msbuildProject": "UnityEngine.XR.LegacyInputHelpers.csproj"}, "UnityGLTF.Helpers/1.0.0": {"type": "project", "path": "UnityGLTF.Helpers.csproj", "msbuildProject": "UnityGLTF.Helpers.csproj"}, "UnityGLTF.Interactivity.Runtime/1.0.0": {"type": "project", "path": "UnityGLTF.Interactivity.Runtime.csproj", "msbuildProject": "UnityGLTF.Interactivity.Runtime.csproj"}, "UnityGLTF.Interactivity.VisualScriptingInstall/1.0.0": {"type": "project", "path": "UnityGLTF.Interactivity.VisualScriptingInstall.csproj", "msbuildProject": "UnityGLTF.Interactivity.VisualScriptingInstall.csproj"}, "UnityGLTFEditor/1.0.0": {"type": "project", "path": "UnityGLTFEditor.csproj", "msbuildProject": "UnityGLTFEditor.csproj"}, "UnityGLTFScripts/1.0.0": {"type": "project", "path": "UnityGLTFScripts.csproj", "msbuildProject": "UnityGLTFScripts.csproj"}}, "projectFileDependencyGroups": {".NETStandard,Version=v2.1": ["Analytics >= 1.0.0", "Assembly-CSharp >= 1.0.0", "Autodesk.Fbx >= 1.0.0", "Autodesk.Fbx.Editor >= 1.0.0", "GLTFSerialization >= 1.0.0", "McpUnity.Editor >= 1.0.0", "Unity.2D.Sprite.Editor >= 1.0.0", "Unity.2D.Tilemap.Editor >= 1.0.0", "Unity.AI.Navigation >= 1.0.0", "Unity.AI.Navigation.Editor >= 1.0.0", "Unity.AI.Navigation.Editor.ConversionSystem >= 1.0.0", "Unity.AI.Navigation.Updater >= 1.0.0", "Unity.Analytics.DataPrivacy >= 1.0.0", "Unity.EditorCoroutines.Editor >= 1.0.0", "Unity.Formats.Fbx.Editor >= 1.0.0", "Unity.Formats.Fbx.Runtime >= 1.0.0", "Unity.Mathematics >= 1.0.0", "Unity.Mathematics.Editor >= 1.0.0", "Unity.PlasticSCM.Editor >= 1.0.0", "Unity.RenderPipelines.Core.Editor >= 1.0.0", "Unity.RenderPipelines.Core.Runtime >= 1.0.0", "Unity.RenderPipelines.Core.ShaderLibrary >= 1.0.0", "Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary >= 1.0.0", "Unity.Rider.Editor >= 1.0.0", "Unity.Searcher.Editor >= 1.0.0", "Unity.Services.Analytics >= 1.0.0", "Unity.Services.Analytics.Editor >= 1.0.0", "Unity.Services.Core >= 1.0.0", "Unity.Services.Core.Analytics >= 1.0.0", "Unity.Services.Core.Environments >= 1.0.0", "Unity.ShaderGraph.Editor >= 1.0.0", "Unity.TextMeshPro >= 1.0.0", "Unity.TextMeshPro.Editor >= 1.0.0", "Unity.Timeline >= 1.0.0", "Unity.Timeline.Editor >= 1.0.0", "Unity.VSCode.Editor >= 1.0.0", "Unity.VisualStudio.Editor >= 1.0.0", "UnityEditor.SpatialTracking >= 1.0.0", "UnityEditor.TestRunner >= 1.0.0", "UnityEditor.UI >= 1.0.0", "UnityEditor.XR.LegacyInputHelpers >= 1.0.0", "UnityEngine.Advertisements >= 1.0.0", "UnityEngine.Advertisements.Editor >= 1.0.0", "UnityEngine.SpatialTracking >= 1.0.0", "UnityEngine.TestRunner >= 1.0.0", "UnityEngine.UI >= 1.0.0", "UnityEngine.XR.LegacyInputHelpers >= 1.0.0", "UnityGLTF.Interactivity.Runtime >= 1.0.0", "UnityGLTF.Interactivity.VisualScriptingInstall >= 1.0.0", "UnityGLTFEditor >= 1.0.0", "UnityGLTFScripts >= 1.0.0"]}, "packageFolders": {"C:\\Users\\<USER>\\.nuget\\packages\\": {}, "C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages": {}}, "project": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Assembly-CSharp-Editor.csproj", "projectName": "Assembly-CSharp-Editor", "projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Assembly-CSharp-Editor.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Temp\\obj\\Debug\\Assembly-CSharp-Editor\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Analytics.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Analytics.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Assembly-CSharp.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Assembly-CSharp.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Autodesk.Fbx.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Autodesk.Fbx.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Autodesk.Fbx.Editor.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Autodesk.Fbx.Editor.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\GLTFSerialization.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\GLTFSerialization.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\McpUnity.Editor.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\McpUnity.Editor.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.2D.Sprite.Editor.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.2D.Sprite.Editor.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.2D.Tilemap.Editor.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.2D.Tilemap.Editor.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.AI.Navigation.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.AI.Navigation.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.AI.Navigation.Editor.ConversionSystem.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.AI.Navigation.Editor.ConversionSystem.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.AI.Navigation.Editor.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.AI.Navigation.Editor.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.AI.Navigation.Updater.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.AI.Navigation.Updater.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.Analytics.DataPrivacy.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.Analytics.DataPrivacy.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.EditorCoroutines.Editor.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.EditorCoroutines.Editor.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.Formats.Fbx.Editor.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.Formats.Fbx.Editor.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.Formats.Fbx.Runtime.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.Formats.Fbx.Runtime.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.Mathematics.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.Mathematics.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.Mathematics.Editor.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.Mathematics.Editor.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.PlasticSCM.Editor.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.PlasticSCM.Editor.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.RenderPipelines.Core.Editor.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.RenderPipelines.Core.Editor.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.RenderPipelines.Core.Runtime.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.RenderPipelines.Core.Runtime.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.RenderPipelines.Core.ShaderLibrary.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.RenderPipelines.Core.ShaderLibrary.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.Rider.Editor.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.Rider.Editor.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.Searcher.Editor.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.Searcher.Editor.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.Services.Analytics.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.Services.Analytics.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.Services.Analytics.Editor.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.Services.Analytics.Editor.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.Services.Core.Analytics.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.Services.Core.Analytics.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.Services.Core.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.Services.Core.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.Services.Core.Environments.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.Services.Core.Environments.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.ShaderGraph.Editor.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.ShaderGraph.Editor.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.TextMeshPro.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.TextMeshPro.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.TextMeshPro.Editor.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.TextMeshPro.Editor.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.Timeline.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.Timeline.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.Timeline.Editor.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.Timeline.Editor.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.VisualStudio.Editor.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.VisualStudio.Editor.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.VSCode.Editor.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\Unity.VSCode.Editor.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEditor.SpatialTracking.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEditor.SpatialTracking.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEditor.TestRunner.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEditor.TestRunner.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEditor.UI.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEditor.UI.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEditor.XR.LegacyInputHelpers.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEditor.XR.LegacyInputHelpers.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEngine.Advertisements.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEngine.Advertisements.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEngine.Advertisements.Editor.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEngine.Advertisements.Editor.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEngine.SpatialTracking.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEngine.SpatialTracking.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEngine.TestRunner.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEngine.TestRunner.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEngine.UI.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEngine.UI.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEngine.XR.LegacyInputHelpers.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityEngine.XR.LegacyInputHelpers.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityGLTF.Interactivity.Runtime.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityGLTF.Interactivity.Runtime.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityGLTF.Interactivity.VisualScriptingInstall.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityGLTF.Interactivity.VisualScriptingInstall.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityGLTFEditor.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityGLTFEditor.csproj"}, "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityGLTFScripts.csproj": {"projectPath": "D:\\BaiduNetdiskDownload\\UnityProject\\AutoColliderSetUp-master\\UnityGLTFScripts.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.203\\RuntimeIdentifierGraph.json"}}}}